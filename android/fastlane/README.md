fastlane documentation
----

# Installation

Make sure you have the latest version of the Xcode command line tools installed:

```sh
xcode-select --install
```

For _fastlane_ installation instructions, see [Installing _fastlane_](https://docs.fastlane.tools/#installing-fastlane)

# Available Actions

## Android

### android prepare_release

```sh
[bundle exec] fastlane android prepare_release
```

Prepare release

### android upload_to_closed_test_internal

```sh
[bundle exec] fastlane android upload_to_closed_test_internal
```

Upload to Closed Test Internal Track

### android dryrun_release

```sh
[bundle exec] fastlane android dryrun_release
```

Dryrun release

----

This README.md is auto-generated and will be re-generated every time [_fastlane_](https://fastlane.tools) is run.

More information about _fastlane_ can be found on [fastlane.tools](https://fastlane.tools).

The documentation of _fastlane_ can be found on [docs.fastlane.tools](https://docs.fastlane.tools).
