# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane
opt_out_usage
default_platform(:android)

platform :android do
  desc "Prepare release"
  lane :prepare_release do
    gradle(task: 'clean')
    app_name = ENV["APP_NAME"]
    version_codes = google_play_track_version_codes(track: "internal")
    previous_build_number = version_codes.max || 0
    new_build_number = previous_build_number + 1
    increment_version_code(gradle_file_path: "samples/#{app_name}/build.gradle.kts", version_code: new_build_number)
    gradle(
      task: ":samples:#{app_name}:bundle",
      build_type: 'release',
      print_command: false,
      flavor: 'internal',
      properties: {
        "android.injected.signing.store.file" => ENV["SIGNING_STORE_FILE"],
        "android.injected.signing.store.password" => ENV["SIGNING_STORE_PASSWORD"],
        "android.injected.signing.key.alias" => ENV["SIGNING_KEY_ALIAS"],
        "android.injected.signing.key.password" => ENV["SIGNING_KEY_PASSWORD"],
      }
    )
  end

  desc "Upload to Closed Test Internal Track"
  lane :upload_to_closed_test_internal do
    prepare_release
    app_name = ENV["APP_NAME"]
    upload_to_play_store(
      track: "internal",
      release_status: 'draft',
      skip_upload_apk: true,
      aab: "samples/#{app_name}/build/outputs/bundle/internalRelease/#{app_name}-internal-release.aab",
      skip_upload_metadata: true,
      skip_upload_images: true,
      skip_upload_screenshots: true
    )
    UI.message("Upload to Closed Test Internal Track complete.")
  end

  desc "Dryrun release"
  lane :dryrun_release do
    prepare_release
    UI.message("Dryrun complete. The release has been prepared but not uploaded to the Play Store.")
  end
end
