// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: modules/messages/point_types.proto

package com.presage.physiology.proto;

public final class PointTypes {
  private PointTypes() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }
  public interface Point2dInt32OrBuilder extends
      // @@protoc_insertion_point(interface_extends:presage.physiology.Point2dInt32)
      com.google.protobuf.MessageLiteOrBuilder {

    /**
     * <code>int32 x = 1;</code>
     * @return The x.
     */
    int getX();

    /**
     * <code>int32 y = 2;</code>
     * @return The y.
     */
    int getY();
  }
  /**
   * Protobuf type {@code presage.physiology.Point2dInt32}
   */
  public  static final class Point2dInt32 extends
      com.google.protobuf.GeneratedMessageLite<
          Point2dInt32, Point2dInt32.Builder> implements
      // @@protoc_insertion_point(message_implements:presage.physiology.Point2dInt32)
      Point2dInt32OrBuilder {
    private Point2dInt32() {
    }
    public static final int X_FIELD_NUMBER = 1;
    private int x_;
    /**
     * <code>int32 x = 1;</code>
     * @return The x.
     */
    @java.lang.Override
    public int getX() {
      return x_;
    }
    /**
     * <code>int32 x = 1;</code>
     * @param value The x to set.
     */
    private void setX(int value) {
      
      x_ = value;
    }
    /**
     * <code>int32 x = 1;</code>
     */
    private void clearX() {
      
      x_ = 0;
    }

    public static final int Y_FIELD_NUMBER = 2;
    private int y_;
    /**
     * <code>int32 y = 2;</code>
     * @return The y.
     */
    @java.lang.Override
    public int getY() {
      return y_;
    }
    /**
     * <code>int32 y = 2;</code>
     * @param value The y to set.
     */
    private void setY(int value) {
      
      y_ = value;
    }
    /**
     * <code>int32 y = 2;</code>
     */
    private void clearY() {
      
      y_ = 0;
    }

    public static com.presage.physiology.proto.PointTypes.Point2dInt32 parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.PointTypes.Point2dInt32 parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.PointTypes.Point2dInt32 parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.PointTypes.Point2dInt32 parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.PointTypes.Point2dInt32 parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.PointTypes.Point2dInt32 parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.PointTypes.Point2dInt32 parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.PointTypes.Point2dInt32 parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static com.presage.physiology.proto.PointTypes.Point2dInt32 parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.PointTypes.Point2dInt32 parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static com.presage.physiology.proto.PointTypes.Point2dInt32 parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.PointTypes.Point2dInt32 parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }

    public static Builder newBuilder() {
      return (Builder) DEFAULT_INSTANCE.createBuilder();
    }
    public static Builder newBuilder(com.presage.physiology.proto.PointTypes.Point2dInt32 prototype) {
      return (Builder) DEFAULT_INSTANCE.createBuilder(prototype);
    }

    /**
     * Protobuf type {@code presage.physiology.Point2dInt32}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageLite.Builder<
          com.presage.physiology.proto.PointTypes.Point2dInt32, Builder> implements
        // @@protoc_insertion_point(builder_implements:presage.physiology.Point2dInt32)
        com.presage.physiology.proto.PointTypes.Point2dInt32OrBuilder {
      // Construct using com.presage.physiology.proto.PointTypes.Point2dInt32.newBuilder()
      private Builder() {
        super(DEFAULT_INSTANCE);
      }


      /**
       * <code>int32 x = 1;</code>
       * @return The x.
       */
      @java.lang.Override
      public int getX() {
        return instance.getX();
      }
      /**
       * <code>int32 x = 1;</code>
       * @param value The x to set.
       * @return This builder for chaining.
       */
      public Builder setX(int value) {
        copyOnWrite();
        instance.setX(value);
        return this;
      }
      /**
       * <code>int32 x = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearX() {
        copyOnWrite();
        instance.clearX();
        return this;
      }

      /**
       * <code>int32 y = 2;</code>
       * @return The y.
       */
      @java.lang.Override
      public int getY() {
        return instance.getY();
      }
      /**
       * <code>int32 y = 2;</code>
       * @param value The y to set.
       * @return This builder for chaining.
       */
      public Builder setY(int value) {
        copyOnWrite();
        instance.setY(value);
        return this;
      }
      /**
       * <code>int32 y = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearY() {
        copyOnWrite();
        instance.clearY();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:presage.physiology.Point2dInt32)
    }
    @java.lang.Override
    @java.lang.SuppressWarnings({"unchecked", "fallthrough"})
    protected final java.lang.Object dynamicMethod(
        com.google.protobuf.GeneratedMessageLite.MethodToInvoke method,
        java.lang.Object arg0, java.lang.Object arg1) {
      switch (method) {
        case NEW_MUTABLE_INSTANCE: {
          return new com.presage.physiology.proto.PointTypes.Point2dInt32();
        }
        case NEW_BUILDER: {
          return new Builder();
        }
        case BUILD_MESSAGE_INFO: {
            java.lang.Object[] objects = new java.lang.Object[] {
              "x_",
              "y_",
            };
            java.lang.String info =
                "\u0000\u0002\u0000\u0000\u0001\u0002\u0002\u0000\u0000\u0000\u0001\u0004\u0002\u0004" +
                "";
            return newMessageInfo(DEFAULT_INSTANCE, info, objects);
        }
        // fall through
        case GET_DEFAULT_INSTANCE: {
          return DEFAULT_INSTANCE;
        }
        case GET_PARSER: {
          com.google.protobuf.Parser<com.presage.physiology.proto.PointTypes.Point2dInt32> parser = PARSER;
          if (parser == null) {
            synchronized (com.presage.physiology.proto.PointTypes.Point2dInt32.class) {
              parser = PARSER;
              if (parser == null) {
                parser =
                    new DefaultInstanceBasedParser<com.presage.physiology.proto.PointTypes.Point2dInt32>(
                        DEFAULT_INSTANCE);
                PARSER = parser;
              }
            }
          }
          return parser;
      }
      case GET_MEMOIZED_IS_INITIALIZED: {
        return (byte) 1;
      }
      case SET_MEMOIZED_IS_INITIALIZED: {
        return null;
      }
      }
      throw new UnsupportedOperationException();
    }


    // @@protoc_insertion_point(class_scope:presage.physiology.Point2dInt32)
    private static final com.presage.physiology.proto.PointTypes.Point2dInt32 DEFAULT_INSTANCE;
    static {
      Point2dInt32 defaultInstance = new Point2dInt32();
      // New instances are implicitly immutable so no need to make
      // immutable.
      DEFAULT_INSTANCE = defaultInstance;
      com.google.protobuf.GeneratedMessageLite.registerDefaultInstance(
        Point2dInt32.class, defaultInstance);
    }

    public static com.presage.physiology.proto.PointTypes.Point2dInt32 getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static volatile com.google.protobuf.Parser<Point2dInt32> PARSER;

    public static com.google.protobuf.Parser<Point2dInt32> parser() {
      return DEFAULT_INSTANCE.getParserForType();
    }
  }

  public interface Point2dFloatOrBuilder extends
      // @@protoc_insertion_point(interface_extends:presage.physiology.Point2dFloat)
      com.google.protobuf.MessageLiteOrBuilder {

    /**
     * <code>float x = 1;</code>
     * @return The x.
     */
    float getX();

    /**
     * <code>float y = 2;</code>
     * @return The y.
     */
    float getY();
  }
  /**
   * Protobuf type {@code presage.physiology.Point2dFloat}
   */
  public  static final class Point2dFloat extends
      com.google.protobuf.GeneratedMessageLite<
          Point2dFloat, Point2dFloat.Builder> implements
      // @@protoc_insertion_point(message_implements:presage.physiology.Point2dFloat)
      Point2dFloatOrBuilder {
    private Point2dFloat() {
    }
    public static final int X_FIELD_NUMBER = 1;
    private float x_;
    /**
     * <code>float x = 1;</code>
     * @return The x.
     */
    @java.lang.Override
    public float getX() {
      return x_;
    }
    /**
     * <code>float x = 1;</code>
     * @param value The x to set.
     */
    private void setX(float value) {
      
      x_ = value;
    }
    /**
     * <code>float x = 1;</code>
     */
    private void clearX() {
      
      x_ = 0F;
    }

    public static final int Y_FIELD_NUMBER = 2;
    private float y_;
    /**
     * <code>float y = 2;</code>
     * @return The y.
     */
    @java.lang.Override
    public float getY() {
      return y_;
    }
    /**
     * <code>float y = 2;</code>
     * @param value The y to set.
     */
    private void setY(float value) {
      
      y_ = value;
    }
    /**
     * <code>float y = 2;</code>
     */
    private void clearY() {
      
      y_ = 0F;
    }

    public static com.presage.physiology.proto.PointTypes.Point2dFloat parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.PointTypes.Point2dFloat parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.PointTypes.Point2dFloat parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.PointTypes.Point2dFloat parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.PointTypes.Point2dFloat parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.PointTypes.Point2dFloat parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.PointTypes.Point2dFloat parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.PointTypes.Point2dFloat parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static com.presage.physiology.proto.PointTypes.Point2dFloat parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.PointTypes.Point2dFloat parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static com.presage.physiology.proto.PointTypes.Point2dFloat parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.PointTypes.Point2dFloat parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }

    public static Builder newBuilder() {
      return (Builder) DEFAULT_INSTANCE.createBuilder();
    }
    public static Builder newBuilder(com.presage.physiology.proto.PointTypes.Point2dFloat prototype) {
      return (Builder) DEFAULT_INSTANCE.createBuilder(prototype);
    }

    /**
     * Protobuf type {@code presage.physiology.Point2dFloat}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageLite.Builder<
          com.presage.physiology.proto.PointTypes.Point2dFloat, Builder> implements
        // @@protoc_insertion_point(builder_implements:presage.physiology.Point2dFloat)
        com.presage.physiology.proto.PointTypes.Point2dFloatOrBuilder {
      // Construct using com.presage.physiology.proto.PointTypes.Point2dFloat.newBuilder()
      private Builder() {
        super(DEFAULT_INSTANCE);
      }


      /**
       * <code>float x = 1;</code>
       * @return The x.
       */
      @java.lang.Override
      public float getX() {
        return instance.getX();
      }
      /**
       * <code>float x = 1;</code>
       * @param value The x to set.
       * @return This builder for chaining.
       */
      public Builder setX(float value) {
        copyOnWrite();
        instance.setX(value);
        return this;
      }
      /**
       * <code>float x = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearX() {
        copyOnWrite();
        instance.clearX();
        return this;
      }

      /**
       * <code>float y = 2;</code>
       * @return The y.
       */
      @java.lang.Override
      public float getY() {
        return instance.getY();
      }
      /**
       * <code>float y = 2;</code>
       * @param value The y to set.
       * @return This builder for chaining.
       */
      public Builder setY(float value) {
        copyOnWrite();
        instance.setY(value);
        return this;
      }
      /**
       * <code>float y = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearY() {
        copyOnWrite();
        instance.clearY();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:presage.physiology.Point2dFloat)
    }
    @java.lang.Override
    @java.lang.SuppressWarnings({"unchecked", "fallthrough"})
    protected final java.lang.Object dynamicMethod(
        com.google.protobuf.GeneratedMessageLite.MethodToInvoke method,
        java.lang.Object arg0, java.lang.Object arg1) {
      switch (method) {
        case NEW_MUTABLE_INSTANCE: {
          return new com.presage.physiology.proto.PointTypes.Point2dFloat();
        }
        case NEW_BUILDER: {
          return new Builder();
        }
        case BUILD_MESSAGE_INFO: {
            java.lang.Object[] objects = new java.lang.Object[] {
              "x_",
              "y_",
            };
            java.lang.String info =
                "\u0000\u0002\u0000\u0000\u0001\u0002\u0002\u0000\u0000\u0000\u0001\u0001\u0002\u0001" +
                "";
            return newMessageInfo(DEFAULT_INSTANCE, info, objects);
        }
        // fall through
        case GET_DEFAULT_INSTANCE: {
          return DEFAULT_INSTANCE;
        }
        case GET_PARSER: {
          com.google.protobuf.Parser<com.presage.physiology.proto.PointTypes.Point2dFloat> parser = PARSER;
          if (parser == null) {
            synchronized (com.presage.physiology.proto.PointTypes.Point2dFloat.class) {
              parser = PARSER;
              if (parser == null) {
                parser =
                    new DefaultInstanceBasedParser<com.presage.physiology.proto.PointTypes.Point2dFloat>(
                        DEFAULT_INSTANCE);
                PARSER = parser;
              }
            }
          }
          return parser;
      }
      case GET_MEMOIZED_IS_INITIALIZED: {
        return (byte) 1;
      }
      case SET_MEMOIZED_IS_INITIALIZED: {
        return null;
      }
      }
      throw new UnsupportedOperationException();
    }


    // @@protoc_insertion_point(class_scope:presage.physiology.Point2dFloat)
    private static final com.presage.physiology.proto.PointTypes.Point2dFloat DEFAULT_INSTANCE;
    static {
      Point2dFloat defaultInstance = new Point2dFloat();
      // New instances are implicitly immutable so no need to make
      // immutable.
      DEFAULT_INSTANCE = defaultInstance;
      com.google.protobuf.GeneratedMessageLite.registerDefaultInstance(
        Point2dFloat.class, defaultInstance);
    }

    public static com.presage.physiology.proto.PointTypes.Point2dFloat getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static volatile com.google.protobuf.Parser<Point2dFloat> PARSER;

    public static com.google.protobuf.Parser<Point2dFloat> parser() {
      return DEFAULT_INSTANCE.getParserForType();
    }
  }

  public interface Point3dFloatOrBuilder extends
      // @@protoc_insertion_point(interface_extends:presage.physiology.Point3dFloat)
      com.google.protobuf.MessageLiteOrBuilder {

    /**
     * <code>float x = 1;</code>
     * @return The x.
     */
    float getX();

    /**
     * <code>float y = 2;</code>
     * @return The y.
     */
    float getY();

    /**
     * <code>float z = 3;</code>
     * @return The z.
     */
    float getZ();
  }
  /**
   * Protobuf type {@code presage.physiology.Point3dFloat}
   */
  public  static final class Point3dFloat extends
      com.google.protobuf.GeneratedMessageLite<
          Point3dFloat, Point3dFloat.Builder> implements
      // @@protoc_insertion_point(message_implements:presage.physiology.Point3dFloat)
      Point3dFloatOrBuilder {
    private Point3dFloat() {
    }
    public static final int X_FIELD_NUMBER = 1;
    private float x_;
    /**
     * <code>float x = 1;</code>
     * @return The x.
     */
    @java.lang.Override
    public float getX() {
      return x_;
    }
    /**
     * <code>float x = 1;</code>
     * @param value The x to set.
     */
    private void setX(float value) {
      
      x_ = value;
    }
    /**
     * <code>float x = 1;</code>
     */
    private void clearX() {
      
      x_ = 0F;
    }

    public static final int Y_FIELD_NUMBER = 2;
    private float y_;
    /**
     * <code>float y = 2;</code>
     * @return The y.
     */
    @java.lang.Override
    public float getY() {
      return y_;
    }
    /**
     * <code>float y = 2;</code>
     * @param value The y to set.
     */
    private void setY(float value) {
      
      y_ = value;
    }
    /**
     * <code>float y = 2;</code>
     */
    private void clearY() {
      
      y_ = 0F;
    }

    public static final int Z_FIELD_NUMBER = 3;
    private float z_;
    /**
     * <code>float z = 3;</code>
     * @return The z.
     */
    @java.lang.Override
    public float getZ() {
      return z_;
    }
    /**
     * <code>float z = 3;</code>
     * @param value The z to set.
     */
    private void setZ(float value) {
      
      z_ = value;
    }
    /**
     * <code>float z = 3;</code>
     */
    private void clearZ() {
      
      z_ = 0F;
    }

    public static com.presage.physiology.proto.PointTypes.Point3dFloat parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.PointTypes.Point3dFloat parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.PointTypes.Point3dFloat parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.PointTypes.Point3dFloat parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.PointTypes.Point3dFloat parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.PointTypes.Point3dFloat parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.PointTypes.Point3dFloat parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.PointTypes.Point3dFloat parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static com.presage.physiology.proto.PointTypes.Point3dFloat parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.PointTypes.Point3dFloat parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static com.presage.physiology.proto.PointTypes.Point3dFloat parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.PointTypes.Point3dFloat parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }

    public static Builder newBuilder() {
      return (Builder) DEFAULT_INSTANCE.createBuilder();
    }
    public static Builder newBuilder(com.presage.physiology.proto.PointTypes.Point3dFloat prototype) {
      return (Builder) DEFAULT_INSTANCE.createBuilder(prototype);
    }

    /**
     * Protobuf type {@code presage.physiology.Point3dFloat}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageLite.Builder<
          com.presage.physiology.proto.PointTypes.Point3dFloat, Builder> implements
        // @@protoc_insertion_point(builder_implements:presage.physiology.Point3dFloat)
        com.presage.physiology.proto.PointTypes.Point3dFloatOrBuilder {
      // Construct using com.presage.physiology.proto.PointTypes.Point3dFloat.newBuilder()
      private Builder() {
        super(DEFAULT_INSTANCE);
      }


      /**
       * <code>float x = 1;</code>
       * @return The x.
       */
      @java.lang.Override
      public float getX() {
        return instance.getX();
      }
      /**
       * <code>float x = 1;</code>
       * @param value The x to set.
       * @return This builder for chaining.
       */
      public Builder setX(float value) {
        copyOnWrite();
        instance.setX(value);
        return this;
      }
      /**
       * <code>float x = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearX() {
        copyOnWrite();
        instance.clearX();
        return this;
      }

      /**
       * <code>float y = 2;</code>
       * @return The y.
       */
      @java.lang.Override
      public float getY() {
        return instance.getY();
      }
      /**
       * <code>float y = 2;</code>
       * @param value The y to set.
       * @return This builder for chaining.
       */
      public Builder setY(float value) {
        copyOnWrite();
        instance.setY(value);
        return this;
      }
      /**
       * <code>float y = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearY() {
        copyOnWrite();
        instance.clearY();
        return this;
      }

      /**
       * <code>float z = 3;</code>
       * @return The z.
       */
      @java.lang.Override
      public float getZ() {
        return instance.getZ();
      }
      /**
       * <code>float z = 3;</code>
       * @param value The z to set.
       * @return This builder for chaining.
       */
      public Builder setZ(float value) {
        copyOnWrite();
        instance.setZ(value);
        return this;
      }
      /**
       * <code>float z = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearZ() {
        copyOnWrite();
        instance.clearZ();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:presage.physiology.Point3dFloat)
    }
    @java.lang.Override
    @java.lang.SuppressWarnings({"unchecked", "fallthrough"})
    protected final java.lang.Object dynamicMethod(
        com.google.protobuf.GeneratedMessageLite.MethodToInvoke method,
        java.lang.Object arg0, java.lang.Object arg1) {
      switch (method) {
        case NEW_MUTABLE_INSTANCE: {
          return new com.presage.physiology.proto.PointTypes.Point3dFloat();
        }
        case NEW_BUILDER: {
          return new Builder();
        }
        case BUILD_MESSAGE_INFO: {
            java.lang.Object[] objects = new java.lang.Object[] {
              "x_",
              "y_",
              "z_",
            };
            java.lang.String info =
                "\u0000\u0003\u0000\u0000\u0001\u0003\u0003\u0000\u0000\u0000\u0001\u0001\u0002\u0001" +
                "\u0003\u0001";
            return newMessageInfo(DEFAULT_INSTANCE, info, objects);
        }
        // fall through
        case GET_DEFAULT_INSTANCE: {
          return DEFAULT_INSTANCE;
        }
        case GET_PARSER: {
          com.google.protobuf.Parser<com.presage.physiology.proto.PointTypes.Point3dFloat> parser = PARSER;
          if (parser == null) {
            synchronized (com.presage.physiology.proto.PointTypes.Point3dFloat.class) {
              parser = PARSER;
              if (parser == null) {
                parser =
                    new DefaultInstanceBasedParser<com.presage.physiology.proto.PointTypes.Point3dFloat>(
                        DEFAULT_INSTANCE);
                PARSER = parser;
              }
            }
          }
          return parser;
      }
      case GET_MEMOIZED_IS_INITIALIZED: {
        return (byte) 1;
      }
      case SET_MEMOIZED_IS_INITIALIZED: {
        return null;
      }
      }
      throw new UnsupportedOperationException();
    }


    // @@protoc_insertion_point(class_scope:presage.physiology.Point3dFloat)
    private static final com.presage.physiology.proto.PointTypes.Point3dFloat DEFAULT_INSTANCE;
    static {
      Point3dFloat defaultInstance = new Point3dFloat();
      // New instances are implicitly immutable so no need to make
      // immutable.
      DEFAULT_INSTANCE = defaultInstance;
      com.google.protobuf.GeneratedMessageLite.registerDefaultInstance(
        Point3dFloat.class, defaultInstance);
    }

    public static com.presage.physiology.proto.PointTypes.Point3dFloat getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static volatile com.google.protobuf.Parser<Point3dFloat> PARSER;

    public static com.google.protobuf.Parser<Point3dFloat> parser() {
      return DEFAULT_INSTANCE.getParserForType();
    }
  }


  static {
  }

  // @@protoc_insertion_point(outer_class_scope)
}
