// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: modules/messages/status.proto

package com.presage.physiology.proto;

public final class StatusProto {
  private StatusProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }
  /**
   * Protobuf enum {@code presage.physiology.StatusCode}
   */
  public enum StatusCode
      implements com.google.protobuf.Internal.EnumLite {
    /**
     * <code>OK = 0;</code>
     */
    OK(0),
    /**
     * <code>NO_FACES_FOUND = 1;</code>
     */
    NO_FACES_FOUND(1),
    /**
     * <code>MORE_THAN_ONE_FACE_FOUND = 2;</code>
     */
    MORE_THAN_ONE_FACE_FOUND(2),
    /**
     * <code>FACE_NOT_CENTERED = 3;</code>
     */
    FACE_NOT_CENTERED(3),
    /**
     * <code>FACE_TOO_BIG_OR_TOO_SMALL = 4;</code>
     */
    FACE_TOO_BIG_OR_TOO_SMALL(4),
    /**
     * <code>IMAGE_TOO_DARK = 5;</code>
     */
    IMAGE_TOO_DARK(5),
    /**
     * <code>IMAGE_TOO_BRIGHT = 6;</code>
     */
    IMAGE_TOO_BRIGHT(6),
    /**
     * <code>CHEST_TOO_FAR_OR_NOT_ENOUGH_SHOWING = 7;</code>
     */
    CHEST_TOO_FAR_OR_NOT_ENOUGH_SHOWING(7),
    /**
     * <code>PROCESSING_NOT_STARTED = 8;</code>
     */
    PROCESSING_NOT_STARTED(8),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>OK = 0;</code>
     */
    public static final int OK_VALUE = 0;
    /**
     * <code>NO_FACES_FOUND = 1;</code>
     */
    public static final int NO_FACES_FOUND_VALUE = 1;
    /**
     * <code>MORE_THAN_ONE_FACE_FOUND = 2;</code>
     */
    public static final int MORE_THAN_ONE_FACE_FOUND_VALUE = 2;
    /**
     * <code>FACE_NOT_CENTERED = 3;</code>
     */
    public static final int FACE_NOT_CENTERED_VALUE = 3;
    /**
     * <code>FACE_TOO_BIG_OR_TOO_SMALL = 4;</code>
     */
    public static final int FACE_TOO_BIG_OR_TOO_SMALL_VALUE = 4;
    /**
     * <code>IMAGE_TOO_DARK = 5;</code>
     */
    public static final int IMAGE_TOO_DARK_VALUE = 5;
    /**
     * <code>IMAGE_TOO_BRIGHT = 6;</code>
     */
    public static final int IMAGE_TOO_BRIGHT_VALUE = 6;
    /**
     * <code>CHEST_TOO_FAR_OR_NOT_ENOUGH_SHOWING = 7;</code>
     */
    public static final int CHEST_TOO_FAR_OR_NOT_ENOUGH_SHOWING_VALUE = 7;
    /**
     * <code>PROCESSING_NOT_STARTED = 8;</code>
     */
    public static final int PROCESSING_NOT_STARTED_VALUE = 8;


    @java.lang.Override
    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The number of the enum to look for.
     * @return The enum associated with the given number.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static StatusCode valueOf(int value) {
      return forNumber(value);
    }

    public static StatusCode forNumber(int value) {
      switch (value) {
        case 0: return OK;
        case 1: return NO_FACES_FOUND;
        case 2: return MORE_THAN_ONE_FACE_FOUND;
        case 3: return FACE_NOT_CENTERED;
        case 4: return FACE_TOO_BIG_OR_TOO_SMALL;
        case 5: return IMAGE_TOO_DARK;
        case 6: return IMAGE_TOO_BRIGHT;
        case 7: return CHEST_TOO_FAR_OR_NOT_ENOUGH_SHOWING;
        case 8: return PROCESSING_NOT_STARTED;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<StatusCode>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        StatusCode> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<StatusCode>() {
            @java.lang.Override
            public StatusCode findValueByNumber(int number) {
              return StatusCode.forNumber(number);
            }
          };

    public static com.google.protobuf.Internal.EnumVerifier 
        internalGetVerifier() {
      return StatusCodeVerifier.INSTANCE;
    }

    private static final class StatusCodeVerifier implements 
         com.google.protobuf.Internal.EnumVerifier { 
            static final com.google.protobuf.Internal.EnumVerifier           INSTANCE = new StatusCodeVerifier();
            @java.lang.Override
            public boolean isInRange(int number) {
              return StatusCode.forNumber(number) != null;
            }
          };

    private final int value;

    private StatusCode(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:presage.physiology.StatusCode)
  }

  public interface StatusValueOrBuilder extends
      // @@protoc_insertion_point(interface_extends:presage.physiology.StatusValue)
      com.google.protobuf.MessageLiteOrBuilder {

    /**
     * <code>.presage.physiology.StatusCode value = 1;</code>
     * @return The enum numeric value on the wire for value.
     */
    int getValueValue();
    /**
     * <code>.presage.physiology.StatusCode value = 1;</code>
     * @return The value.
     */
    com.presage.physiology.proto.StatusProto.StatusCode getValue();
  }
  /**
   * Protobuf type {@code presage.physiology.StatusValue}
   */
  public  static final class StatusValue extends
      com.google.protobuf.GeneratedMessageLite<
          StatusValue, StatusValue.Builder> implements
      // @@protoc_insertion_point(message_implements:presage.physiology.StatusValue)
      StatusValueOrBuilder {
    private StatusValue() {
    }
    public static final int VALUE_FIELD_NUMBER = 1;
    private int value_;
    /**
     * <code>.presage.physiology.StatusCode value = 1;</code>
     * @return The enum numeric value on the wire for value.
     */
    @java.lang.Override
    public int getValueValue() {
      return value_;
    }
    /**
     * <code>.presage.physiology.StatusCode value = 1;</code>
     * @return The value.
     */
    @java.lang.Override
    public com.presage.physiology.proto.StatusProto.StatusCode getValue() {
      com.presage.physiology.proto.StatusProto.StatusCode result = com.presage.physiology.proto.StatusProto.StatusCode.forNumber(value_);
      return result == null ? com.presage.physiology.proto.StatusProto.StatusCode.UNRECOGNIZED : result;
    }
    /**
     * <code>.presage.physiology.StatusCode value = 1;</code>
     * @param value The enum numeric value on the wire for value to set.
     */
    private void setValueValue(int value) {
        value_ = value;
    }
    /**
     * <code>.presage.physiology.StatusCode value = 1;</code>
     * @param value The value to set.
     */
    private void setValue(com.presage.physiology.proto.StatusProto.StatusCode value) {
      value_ = value.getNumber();
      
    }
    /**
     * <code>.presage.physiology.StatusCode value = 1;</code>
     */
    private void clearValue() {
      
      value_ = 0;
    }

    public static com.presage.physiology.proto.StatusProto.StatusValue parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.StatusProto.StatusValue parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.StatusProto.StatusValue parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.StatusProto.StatusValue parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.StatusProto.StatusValue parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.StatusProto.StatusValue parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.StatusProto.StatusValue parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.StatusProto.StatusValue parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static com.presage.physiology.proto.StatusProto.StatusValue parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.StatusProto.StatusValue parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static com.presage.physiology.proto.StatusProto.StatusValue parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.StatusProto.StatusValue parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }

    public static Builder newBuilder() {
      return (Builder) DEFAULT_INSTANCE.createBuilder();
    }
    public static Builder newBuilder(com.presage.physiology.proto.StatusProto.StatusValue prototype) {
      return (Builder) DEFAULT_INSTANCE.createBuilder(prototype);
    }

    /**
     * Protobuf type {@code presage.physiology.StatusValue}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageLite.Builder<
          com.presage.physiology.proto.StatusProto.StatusValue, Builder> implements
        // @@protoc_insertion_point(builder_implements:presage.physiology.StatusValue)
        com.presage.physiology.proto.StatusProto.StatusValueOrBuilder {
      // Construct using com.presage.physiology.proto.StatusProto.StatusValue.newBuilder()
      private Builder() {
        super(DEFAULT_INSTANCE);
      }


      /**
       * <code>.presage.physiology.StatusCode value = 1;</code>
       * @return The enum numeric value on the wire for value.
       */
      @java.lang.Override
      public int getValueValue() {
        return instance.getValueValue();
      }
      /**
       * <code>.presage.physiology.StatusCode value = 1;</code>
       * @param value The value to set.
       * @return This builder for chaining.
       */
      public Builder setValueValue(int value) {
        copyOnWrite();
        instance.setValueValue(value);
        return this;
      }
      /**
       * <code>.presage.physiology.StatusCode value = 1;</code>
       * @return The value.
       */
      @java.lang.Override
      public com.presage.physiology.proto.StatusProto.StatusCode getValue() {
        return instance.getValue();
      }
      /**
       * <code>.presage.physiology.StatusCode value = 1;</code>
       * @param value The enum numeric value on the wire for value to set.
       * @return This builder for chaining.
       */
      public Builder setValue(com.presage.physiology.proto.StatusProto.StatusCode value) {
        copyOnWrite();
        instance.setValue(value);
        return this;
      }
      /**
       * <code>.presage.physiology.StatusCode value = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearValue() {
        copyOnWrite();
        instance.clearValue();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:presage.physiology.StatusValue)
    }
    @java.lang.Override
    @java.lang.SuppressWarnings({"unchecked", "fallthrough"})
    protected final java.lang.Object dynamicMethod(
        com.google.protobuf.GeneratedMessageLite.MethodToInvoke method,
        java.lang.Object arg0, java.lang.Object arg1) {
      switch (method) {
        case NEW_MUTABLE_INSTANCE: {
          return new com.presage.physiology.proto.StatusProto.StatusValue();
        }
        case NEW_BUILDER: {
          return new Builder();
        }
        case BUILD_MESSAGE_INFO: {
            java.lang.Object[] objects = new java.lang.Object[] {
              "value_",
            };
            java.lang.String info =
                "\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0000\u0000\u0001\f";
            return newMessageInfo(DEFAULT_INSTANCE, info, objects);
        }
        // fall through
        case GET_DEFAULT_INSTANCE: {
          return DEFAULT_INSTANCE;
        }
        case GET_PARSER: {
          com.google.protobuf.Parser<com.presage.physiology.proto.StatusProto.StatusValue> parser = PARSER;
          if (parser == null) {
            synchronized (com.presage.physiology.proto.StatusProto.StatusValue.class) {
              parser = PARSER;
              if (parser == null) {
                parser =
                    new DefaultInstanceBasedParser<com.presage.physiology.proto.StatusProto.StatusValue>(
                        DEFAULT_INSTANCE);
                PARSER = parser;
              }
            }
          }
          return parser;
      }
      case GET_MEMOIZED_IS_INITIALIZED: {
        return (byte) 1;
      }
      case SET_MEMOIZED_IS_INITIALIZED: {
        return null;
      }
      }
      throw new UnsupportedOperationException();
    }


    // @@protoc_insertion_point(class_scope:presage.physiology.StatusValue)
    private static final com.presage.physiology.proto.StatusProto.StatusValue DEFAULT_INSTANCE;
    static {
      StatusValue defaultInstance = new StatusValue();
      // New instances are implicitly immutable so no need to make
      // immutable.
      DEFAULT_INSTANCE = defaultInstance;
      com.google.protobuf.GeneratedMessageLite.registerDefaultInstance(
        StatusValue.class, defaultInstance);
    }

    public static com.presage.physiology.proto.StatusProto.StatusValue getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static volatile com.google.protobuf.Parser<StatusValue> PARSER;

    public static com.google.protobuf.Parser<StatusValue> parser() {
      return DEFAULT_INSTANCE.getParserForType();
    }
  }


  static {
  }

  // @@protoc_insertion_point(outer_class_scope)
}
