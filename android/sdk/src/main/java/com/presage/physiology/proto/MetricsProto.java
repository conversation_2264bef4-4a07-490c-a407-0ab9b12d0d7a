// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: modules/messages/metrics.proto

package com.presage.physiology.proto;

public final class MetricsProto {
  private MetricsProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }
  public interface MeasurementOrBuilder extends
      // @@protoc_insertion_point(interface_extends:presage.physiology.Measurement)
      com.google.protobuf.MessageLiteOrBuilder {

    /**
     * <code>float time = 1;</code>
     * @return The time.
     */
    float getTime();

    /**
     * <code>float value = 2;</code>
     * @return The value.
     */
    float getValue();

    /**
     * <code>bool stable = 3;</code>
     * @return The stable.
     */
    boolean getStable();
  }
  /**
   * Protobuf type {@code presage.physiology.Measurement}
   */
  public  static final class Measurement extends
      com.google.protobuf.GeneratedMessageLite<
          Measurement, Measurement.Builder> implements
      // @@protoc_insertion_point(message_implements:presage.physiology.Measurement)
      MeasurementOrBuilder {
    private Measurement() {
    }
    public static final int TIME_FIELD_NUMBER = 1;
    private float time_;
    /**
     * <code>float time = 1;</code>
     * @return The time.
     */
    @java.lang.Override
    public float getTime() {
      return time_;
    }
    /**
     * <code>float time = 1;</code>
     * @param value The time to set.
     */
    private void setTime(float value) {
      
      time_ = value;
    }
    /**
     * <code>float time = 1;</code>
     */
    private void clearTime() {
      
      time_ = 0F;
    }

    public static final int VALUE_FIELD_NUMBER = 2;
    private float value_;
    /**
     * <code>float value = 2;</code>
     * @return The value.
     */
    @java.lang.Override
    public float getValue() {
      return value_;
    }
    /**
     * <code>float value = 2;</code>
     * @param value The value to set.
     */
    private void setValue(float value) {
      
      value_ = value;
    }
    /**
     * <code>float value = 2;</code>
     */
    private void clearValue() {
      
      value_ = 0F;
    }

    public static final int STABLE_FIELD_NUMBER = 3;
    private boolean stable_;
    /**
     * <code>bool stable = 3;</code>
     * @return The stable.
     */
    @java.lang.Override
    public boolean getStable() {
      return stable_;
    }
    /**
     * <code>bool stable = 3;</code>
     * @param value The stable to set.
     */
    private void setStable(boolean value) {
      
      stable_ = value;
    }
    /**
     * <code>bool stable = 3;</code>
     */
    private void clearStable() {
      
      stable_ = false;
    }

    public static com.presage.physiology.proto.MetricsProto.Measurement parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.MetricsProto.Measurement parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.Measurement parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.MetricsProto.Measurement parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.Measurement parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.MetricsProto.Measurement parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.Measurement parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.MetricsProto.Measurement parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.Measurement parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.MetricsProto.Measurement parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.Measurement parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.MetricsProto.Measurement parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }

    public static Builder newBuilder() {
      return (Builder) DEFAULT_INSTANCE.createBuilder();
    }
    public static Builder newBuilder(com.presage.physiology.proto.MetricsProto.Measurement prototype) {
      return (Builder) DEFAULT_INSTANCE.createBuilder(prototype);
    }

    /**
     * Protobuf type {@code presage.physiology.Measurement}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageLite.Builder<
          com.presage.physiology.proto.MetricsProto.Measurement, Builder> implements
        // @@protoc_insertion_point(builder_implements:presage.physiology.Measurement)
        com.presage.physiology.proto.MetricsProto.MeasurementOrBuilder {
      // Construct using com.presage.physiology.proto.MetricsProto.Measurement.newBuilder()
      private Builder() {
        super(DEFAULT_INSTANCE);
      }


      /**
       * <code>float time = 1;</code>
       * @return The time.
       */
      @java.lang.Override
      public float getTime() {
        return instance.getTime();
      }
      /**
       * <code>float time = 1;</code>
       * @param value The time to set.
       * @return This builder for chaining.
       */
      public Builder setTime(float value) {
        copyOnWrite();
        instance.setTime(value);
        return this;
      }
      /**
       * <code>float time = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTime() {
        copyOnWrite();
        instance.clearTime();
        return this;
      }

      /**
       * <code>float value = 2;</code>
       * @return The value.
       */
      @java.lang.Override
      public float getValue() {
        return instance.getValue();
      }
      /**
       * <code>float value = 2;</code>
       * @param value The value to set.
       * @return This builder for chaining.
       */
      public Builder setValue(float value) {
        copyOnWrite();
        instance.setValue(value);
        return this;
      }
      /**
       * <code>float value = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearValue() {
        copyOnWrite();
        instance.clearValue();
        return this;
      }

      /**
       * <code>bool stable = 3;</code>
       * @return The stable.
       */
      @java.lang.Override
      public boolean getStable() {
        return instance.getStable();
      }
      /**
       * <code>bool stable = 3;</code>
       * @param value The stable to set.
       * @return This builder for chaining.
       */
      public Builder setStable(boolean value) {
        copyOnWrite();
        instance.setStable(value);
        return this;
      }
      /**
       * <code>bool stable = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearStable() {
        copyOnWrite();
        instance.clearStable();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:presage.physiology.Measurement)
    }
    @java.lang.Override
    @java.lang.SuppressWarnings({"unchecked", "fallthrough"})
    protected final java.lang.Object dynamicMethod(
        com.google.protobuf.GeneratedMessageLite.MethodToInvoke method,
        java.lang.Object arg0, java.lang.Object arg1) {
      switch (method) {
        case NEW_MUTABLE_INSTANCE: {
          return new com.presage.physiology.proto.MetricsProto.Measurement();
        }
        case NEW_BUILDER: {
          return new Builder();
        }
        case BUILD_MESSAGE_INFO: {
            java.lang.Object[] objects = new java.lang.Object[] {
              "time_",
              "value_",
              "stable_",
            };
            java.lang.String info =
                "\u0000\u0003\u0000\u0000\u0001\u0003\u0003\u0000\u0000\u0000\u0001\u0001\u0002\u0001" +
                "\u0003\u0007";
            return newMessageInfo(DEFAULT_INSTANCE, info, objects);
        }
        // fall through
        case GET_DEFAULT_INSTANCE: {
          return DEFAULT_INSTANCE;
        }
        case GET_PARSER: {
          com.google.protobuf.Parser<com.presage.physiology.proto.MetricsProto.Measurement> parser = PARSER;
          if (parser == null) {
            synchronized (com.presage.physiology.proto.MetricsProto.Measurement.class) {
              parser = PARSER;
              if (parser == null) {
                parser =
                    new DefaultInstanceBasedParser<com.presage.physiology.proto.MetricsProto.Measurement>(
                        DEFAULT_INSTANCE);
                PARSER = parser;
              }
            }
          }
          return parser;
      }
      case GET_MEMOIZED_IS_INITIALIZED: {
        return (byte) 1;
      }
      case SET_MEMOIZED_IS_INITIALIZED: {
        return null;
      }
      }
      throw new UnsupportedOperationException();
    }


    // @@protoc_insertion_point(class_scope:presage.physiology.Measurement)
    private static final com.presage.physiology.proto.MetricsProto.Measurement DEFAULT_INSTANCE;
    static {
      Measurement defaultInstance = new Measurement();
      // New instances are implicitly immutable so no need to make
      // immutable.
      DEFAULT_INSTANCE = defaultInstance;
      com.google.protobuf.GeneratedMessageLite.registerDefaultInstance(
        Measurement.class, defaultInstance);
    }

    public static com.presage.physiology.proto.MetricsProto.Measurement getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static volatile com.google.protobuf.Parser<Measurement> PARSER;

    public static com.google.protobuf.Parser<Measurement> parser() {
      return DEFAULT_INSTANCE.getParserForType();
    }
  }

  public interface DetectionStatusOrBuilder extends
      // @@protoc_insertion_point(interface_extends:presage.physiology.DetectionStatus)
      com.google.protobuf.MessageLiteOrBuilder {

    /**
     * <code>float time = 1;</code>
     * @return The time.
     */
    float getTime();

    /**
     * <code>bool detected = 2;</code>
     * @return The detected.
     */
    boolean getDetected();

    /**
     * <code>bool stable = 3;</code>
     * @return The stable.
     */
    boolean getStable();
  }
  /**
   * Protobuf type {@code presage.physiology.DetectionStatus}
   */
  public  static final class DetectionStatus extends
      com.google.protobuf.GeneratedMessageLite<
          DetectionStatus, DetectionStatus.Builder> implements
      // @@protoc_insertion_point(message_implements:presage.physiology.DetectionStatus)
      DetectionStatusOrBuilder {
    private DetectionStatus() {
    }
    public static final int TIME_FIELD_NUMBER = 1;
    private float time_;
    /**
     * <code>float time = 1;</code>
     * @return The time.
     */
    @java.lang.Override
    public float getTime() {
      return time_;
    }
    /**
     * <code>float time = 1;</code>
     * @param value The time to set.
     */
    private void setTime(float value) {
      
      time_ = value;
    }
    /**
     * <code>float time = 1;</code>
     */
    private void clearTime() {
      
      time_ = 0F;
    }

    public static final int DETECTED_FIELD_NUMBER = 2;
    private boolean detected_;
    /**
     * <code>bool detected = 2;</code>
     * @return The detected.
     */
    @java.lang.Override
    public boolean getDetected() {
      return detected_;
    }
    /**
     * <code>bool detected = 2;</code>
     * @param value The detected to set.
     */
    private void setDetected(boolean value) {
      
      detected_ = value;
    }
    /**
     * <code>bool detected = 2;</code>
     */
    private void clearDetected() {
      
      detected_ = false;
    }

    public static final int STABLE_FIELD_NUMBER = 3;
    private boolean stable_;
    /**
     * <code>bool stable = 3;</code>
     * @return The stable.
     */
    @java.lang.Override
    public boolean getStable() {
      return stable_;
    }
    /**
     * <code>bool stable = 3;</code>
     * @param value The stable to set.
     */
    private void setStable(boolean value) {
      
      stable_ = value;
    }
    /**
     * <code>bool stable = 3;</code>
     */
    private void clearStable() {
      
      stable_ = false;
    }

    public static com.presage.physiology.proto.MetricsProto.DetectionStatus parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.MetricsProto.DetectionStatus parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.DetectionStatus parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.MetricsProto.DetectionStatus parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.DetectionStatus parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.MetricsProto.DetectionStatus parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.DetectionStatus parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.MetricsProto.DetectionStatus parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.DetectionStatus parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.MetricsProto.DetectionStatus parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.DetectionStatus parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.MetricsProto.DetectionStatus parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }

    public static Builder newBuilder() {
      return (Builder) DEFAULT_INSTANCE.createBuilder();
    }
    public static Builder newBuilder(com.presage.physiology.proto.MetricsProto.DetectionStatus prototype) {
      return (Builder) DEFAULT_INSTANCE.createBuilder(prototype);
    }

    /**
     * Protobuf type {@code presage.physiology.DetectionStatus}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageLite.Builder<
          com.presage.physiology.proto.MetricsProto.DetectionStatus, Builder> implements
        // @@protoc_insertion_point(builder_implements:presage.physiology.DetectionStatus)
        com.presage.physiology.proto.MetricsProto.DetectionStatusOrBuilder {
      // Construct using com.presage.physiology.proto.MetricsProto.DetectionStatus.newBuilder()
      private Builder() {
        super(DEFAULT_INSTANCE);
      }


      /**
       * <code>float time = 1;</code>
       * @return The time.
       */
      @java.lang.Override
      public float getTime() {
        return instance.getTime();
      }
      /**
       * <code>float time = 1;</code>
       * @param value The time to set.
       * @return This builder for chaining.
       */
      public Builder setTime(float value) {
        copyOnWrite();
        instance.setTime(value);
        return this;
      }
      /**
       * <code>float time = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTime() {
        copyOnWrite();
        instance.clearTime();
        return this;
      }

      /**
       * <code>bool detected = 2;</code>
       * @return The detected.
       */
      @java.lang.Override
      public boolean getDetected() {
        return instance.getDetected();
      }
      /**
       * <code>bool detected = 2;</code>
       * @param value The detected to set.
       * @return This builder for chaining.
       */
      public Builder setDetected(boolean value) {
        copyOnWrite();
        instance.setDetected(value);
        return this;
      }
      /**
       * <code>bool detected = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearDetected() {
        copyOnWrite();
        instance.clearDetected();
        return this;
      }

      /**
       * <code>bool stable = 3;</code>
       * @return The stable.
       */
      @java.lang.Override
      public boolean getStable() {
        return instance.getStable();
      }
      /**
       * <code>bool stable = 3;</code>
       * @param value The stable to set.
       * @return This builder for chaining.
       */
      public Builder setStable(boolean value) {
        copyOnWrite();
        instance.setStable(value);
        return this;
      }
      /**
       * <code>bool stable = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearStable() {
        copyOnWrite();
        instance.clearStable();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:presage.physiology.DetectionStatus)
    }
    @java.lang.Override
    @java.lang.SuppressWarnings({"unchecked", "fallthrough"})
    protected final java.lang.Object dynamicMethod(
        com.google.protobuf.GeneratedMessageLite.MethodToInvoke method,
        java.lang.Object arg0, java.lang.Object arg1) {
      switch (method) {
        case NEW_MUTABLE_INSTANCE: {
          return new com.presage.physiology.proto.MetricsProto.DetectionStatus();
        }
        case NEW_BUILDER: {
          return new Builder();
        }
        case BUILD_MESSAGE_INFO: {
            java.lang.Object[] objects = new java.lang.Object[] {
              "time_",
              "detected_",
              "stable_",
            };
            java.lang.String info =
                "\u0000\u0003\u0000\u0000\u0001\u0003\u0003\u0000\u0000\u0000\u0001\u0001\u0002\u0007" +
                "\u0003\u0007";
            return newMessageInfo(DEFAULT_INSTANCE, info, objects);
        }
        // fall through
        case GET_DEFAULT_INSTANCE: {
          return DEFAULT_INSTANCE;
        }
        case GET_PARSER: {
          com.google.protobuf.Parser<com.presage.physiology.proto.MetricsProto.DetectionStatus> parser = PARSER;
          if (parser == null) {
            synchronized (com.presage.physiology.proto.MetricsProto.DetectionStatus.class) {
              parser = PARSER;
              if (parser == null) {
                parser =
                    new DefaultInstanceBasedParser<com.presage.physiology.proto.MetricsProto.DetectionStatus>(
                        DEFAULT_INSTANCE);
                PARSER = parser;
              }
            }
          }
          return parser;
      }
      case GET_MEMOIZED_IS_INITIALIZED: {
        return (byte) 1;
      }
      case SET_MEMOIZED_IS_INITIALIZED: {
        return null;
      }
      }
      throw new UnsupportedOperationException();
    }


    // @@protoc_insertion_point(class_scope:presage.physiology.DetectionStatus)
    private static final com.presage.physiology.proto.MetricsProto.DetectionStatus DEFAULT_INSTANCE;
    static {
      DetectionStatus defaultInstance = new DetectionStatus();
      // New instances are implicitly immutable so no need to make
      // immutable.
      DEFAULT_INSTANCE = defaultInstance;
      com.google.protobuf.GeneratedMessageLite.registerDefaultInstance(
        DetectionStatus.class, defaultInstance);
    }

    public static com.presage.physiology.proto.MetricsProto.DetectionStatus getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static volatile com.google.protobuf.Parser<DetectionStatus> PARSER;

    public static com.google.protobuf.Parser<DetectionStatus> parser() {
      return DEFAULT_INSTANCE.getParserForType();
    }
  }

  public interface MeasurementWithConfidenceOrBuilder extends
      // @@protoc_insertion_point(interface_extends:presage.physiology.MeasurementWithConfidence)
      com.google.protobuf.MessageLiteOrBuilder {

    /**
     * <code>float time = 1;</code>
     * @return The time.
     */
    float getTime();

    /**
     * <code>float value = 2;</code>
     * @return The value.
     */
    float getValue();

    /**
     * <code>bool stable = 3;</code>
     * @return The stable.
     */
    boolean getStable();

    /**
     * <code>float confidence = 4;</code>
     * @return The confidence.
     */
    float getConfidence();
  }
  /**
   * Protobuf type {@code presage.physiology.MeasurementWithConfidence}
   */
  public  static final class MeasurementWithConfidence extends
      com.google.protobuf.GeneratedMessageLite<
          MeasurementWithConfidence, MeasurementWithConfidence.Builder> implements
      // @@protoc_insertion_point(message_implements:presage.physiology.MeasurementWithConfidence)
      MeasurementWithConfidenceOrBuilder {
    private MeasurementWithConfidence() {
    }
    public static final int TIME_FIELD_NUMBER = 1;
    private float time_;
    /**
     * <code>float time = 1;</code>
     * @return The time.
     */
    @java.lang.Override
    public float getTime() {
      return time_;
    }
    /**
     * <code>float time = 1;</code>
     * @param value The time to set.
     */
    private void setTime(float value) {
      
      time_ = value;
    }
    /**
     * <code>float time = 1;</code>
     */
    private void clearTime() {
      
      time_ = 0F;
    }

    public static final int VALUE_FIELD_NUMBER = 2;
    private float value_;
    /**
     * <code>float value = 2;</code>
     * @return The value.
     */
    @java.lang.Override
    public float getValue() {
      return value_;
    }
    /**
     * <code>float value = 2;</code>
     * @param value The value to set.
     */
    private void setValue(float value) {
      
      value_ = value;
    }
    /**
     * <code>float value = 2;</code>
     */
    private void clearValue() {
      
      value_ = 0F;
    }

    public static final int STABLE_FIELD_NUMBER = 3;
    private boolean stable_;
    /**
     * <code>bool stable = 3;</code>
     * @return The stable.
     */
    @java.lang.Override
    public boolean getStable() {
      return stable_;
    }
    /**
     * <code>bool stable = 3;</code>
     * @param value The stable to set.
     */
    private void setStable(boolean value) {
      
      stable_ = value;
    }
    /**
     * <code>bool stable = 3;</code>
     */
    private void clearStable() {
      
      stable_ = false;
    }

    public static final int CONFIDENCE_FIELD_NUMBER = 4;
    private float confidence_;
    /**
     * <code>float confidence = 4;</code>
     * @return The confidence.
     */
    @java.lang.Override
    public float getConfidence() {
      return confidence_;
    }
    /**
     * <code>float confidence = 4;</code>
     * @param value The confidence to set.
     */
    private void setConfidence(float value) {
      
      confidence_ = value;
    }
    /**
     * <code>float confidence = 4;</code>
     */
    private void clearConfidence() {
      
      confidence_ = 0F;
    }

    public static com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }

    public static Builder newBuilder() {
      return (Builder) DEFAULT_INSTANCE.createBuilder();
    }
    public static Builder newBuilder(com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence prototype) {
      return (Builder) DEFAULT_INSTANCE.createBuilder(prototype);
    }

    /**
     * Protobuf type {@code presage.physiology.MeasurementWithConfidence}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageLite.Builder<
          com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence, Builder> implements
        // @@protoc_insertion_point(builder_implements:presage.physiology.MeasurementWithConfidence)
        com.presage.physiology.proto.MetricsProto.MeasurementWithConfidenceOrBuilder {
      // Construct using com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence.newBuilder()
      private Builder() {
        super(DEFAULT_INSTANCE);
      }


      /**
       * <code>float time = 1;</code>
       * @return The time.
       */
      @java.lang.Override
      public float getTime() {
        return instance.getTime();
      }
      /**
       * <code>float time = 1;</code>
       * @param value The time to set.
       * @return This builder for chaining.
       */
      public Builder setTime(float value) {
        copyOnWrite();
        instance.setTime(value);
        return this;
      }
      /**
       * <code>float time = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTime() {
        copyOnWrite();
        instance.clearTime();
        return this;
      }

      /**
       * <code>float value = 2;</code>
       * @return The value.
       */
      @java.lang.Override
      public float getValue() {
        return instance.getValue();
      }
      /**
       * <code>float value = 2;</code>
       * @param value The value to set.
       * @return This builder for chaining.
       */
      public Builder setValue(float value) {
        copyOnWrite();
        instance.setValue(value);
        return this;
      }
      /**
       * <code>float value = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearValue() {
        copyOnWrite();
        instance.clearValue();
        return this;
      }

      /**
       * <code>bool stable = 3;</code>
       * @return The stable.
       */
      @java.lang.Override
      public boolean getStable() {
        return instance.getStable();
      }
      /**
       * <code>bool stable = 3;</code>
       * @param value The stable to set.
       * @return This builder for chaining.
       */
      public Builder setStable(boolean value) {
        copyOnWrite();
        instance.setStable(value);
        return this;
      }
      /**
       * <code>bool stable = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearStable() {
        copyOnWrite();
        instance.clearStable();
        return this;
      }

      /**
       * <code>float confidence = 4;</code>
       * @return The confidence.
       */
      @java.lang.Override
      public float getConfidence() {
        return instance.getConfidence();
      }
      /**
       * <code>float confidence = 4;</code>
       * @param value The confidence to set.
       * @return This builder for chaining.
       */
      public Builder setConfidence(float value) {
        copyOnWrite();
        instance.setConfidence(value);
        return this;
      }
      /**
       * <code>float confidence = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearConfidence() {
        copyOnWrite();
        instance.clearConfidence();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:presage.physiology.MeasurementWithConfidence)
    }
    @java.lang.Override
    @java.lang.SuppressWarnings({"unchecked", "fallthrough"})
    protected final java.lang.Object dynamicMethod(
        com.google.protobuf.GeneratedMessageLite.MethodToInvoke method,
        java.lang.Object arg0, java.lang.Object arg1) {
      switch (method) {
        case NEW_MUTABLE_INSTANCE: {
          return new com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence();
        }
        case NEW_BUILDER: {
          return new Builder();
        }
        case BUILD_MESSAGE_INFO: {
            java.lang.Object[] objects = new java.lang.Object[] {
              "time_",
              "value_",
              "stable_",
              "confidence_",
            };
            java.lang.String info =
                "\u0000\u0004\u0000\u0000\u0001\u0004\u0004\u0000\u0000\u0000\u0001\u0001\u0002\u0001" +
                "\u0003\u0007\u0004\u0001";
            return newMessageInfo(DEFAULT_INSTANCE, info, objects);
        }
        // fall through
        case GET_DEFAULT_INSTANCE: {
          return DEFAULT_INSTANCE;
        }
        case GET_PARSER: {
          com.google.protobuf.Parser<com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence> parser = PARSER;
          if (parser == null) {
            synchronized (com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence.class) {
              parser = PARSER;
              if (parser == null) {
                parser =
                    new DefaultInstanceBasedParser<com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence>(
                        DEFAULT_INSTANCE);
                PARSER = parser;
              }
            }
          }
          return parser;
      }
      case GET_MEMOIZED_IS_INITIALIZED: {
        return (byte) 1;
      }
      case SET_MEMOIZED_IS_INITIALIZED: {
        return null;
      }
      }
      throw new UnsupportedOperationException();
    }


    // @@protoc_insertion_point(class_scope:presage.physiology.MeasurementWithConfidence)
    private static final com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence DEFAULT_INSTANCE;
    static {
      MeasurementWithConfidence defaultInstance = new MeasurementWithConfidence();
      // New instances are implicitly immutable so no need to make
      // immutable.
      DEFAULT_INSTANCE = defaultInstance;
      com.google.protobuf.GeneratedMessageLite.registerDefaultInstance(
        MeasurementWithConfidence.class, defaultInstance);
    }

    public static com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static volatile com.google.protobuf.Parser<MeasurementWithConfidence> PARSER;

    public static com.google.protobuf.Parser<MeasurementWithConfidence> parser() {
      return DEFAULT_INSTANCE.getParserForType();
    }
  }

  public interface StrictOrBuilder extends
      // @@protoc_insertion_point(interface_extends:presage.physiology.Strict)
      com.google.protobuf.MessageLiteOrBuilder {

    /**
     * <code>float value = 1;</code>
     * @return The value.
     */
    float getValue();
  }
  /**
   * Protobuf type {@code presage.physiology.Strict}
   */
  public  static final class Strict extends
      com.google.protobuf.GeneratedMessageLite<
          Strict, Strict.Builder> implements
      // @@protoc_insertion_point(message_implements:presage.physiology.Strict)
      StrictOrBuilder {
    private Strict() {
    }
    public static final int VALUE_FIELD_NUMBER = 1;
    private float value_;
    /**
     * <code>float value = 1;</code>
     * @return The value.
     */
    @java.lang.Override
    public float getValue() {
      return value_;
    }
    /**
     * <code>float value = 1;</code>
     * @param value The value to set.
     */
    private void setValue(float value) {
      
      value_ = value;
    }
    /**
     * <code>float value = 1;</code>
     */
    private void clearValue() {
      
      value_ = 0F;
    }

    public static com.presage.physiology.proto.MetricsProto.Strict parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.MetricsProto.Strict parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.Strict parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.MetricsProto.Strict parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.Strict parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.MetricsProto.Strict parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.Strict parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.MetricsProto.Strict parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.Strict parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.MetricsProto.Strict parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.Strict parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.MetricsProto.Strict parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }

    public static Builder newBuilder() {
      return (Builder) DEFAULT_INSTANCE.createBuilder();
    }
    public static Builder newBuilder(com.presage.physiology.proto.MetricsProto.Strict prototype) {
      return (Builder) DEFAULT_INSTANCE.createBuilder(prototype);
    }

    /**
     * Protobuf type {@code presage.physiology.Strict}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageLite.Builder<
          com.presage.physiology.proto.MetricsProto.Strict, Builder> implements
        // @@protoc_insertion_point(builder_implements:presage.physiology.Strict)
        com.presage.physiology.proto.MetricsProto.StrictOrBuilder {
      // Construct using com.presage.physiology.proto.MetricsProto.Strict.newBuilder()
      private Builder() {
        super(DEFAULT_INSTANCE);
      }


      /**
       * <code>float value = 1;</code>
       * @return The value.
       */
      @java.lang.Override
      public float getValue() {
        return instance.getValue();
      }
      /**
       * <code>float value = 1;</code>
       * @param value The value to set.
       * @return This builder for chaining.
       */
      public Builder setValue(float value) {
        copyOnWrite();
        instance.setValue(value);
        return this;
      }
      /**
       * <code>float value = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearValue() {
        copyOnWrite();
        instance.clearValue();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:presage.physiology.Strict)
    }
    @java.lang.Override
    @java.lang.SuppressWarnings({"unchecked", "fallthrough"})
    protected final java.lang.Object dynamicMethod(
        com.google.protobuf.GeneratedMessageLite.MethodToInvoke method,
        java.lang.Object arg0, java.lang.Object arg1) {
      switch (method) {
        case NEW_MUTABLE_INSTANCE: {
          return new com.presage.physiology.proto.MetricsProto.Strict();
        }
        case NEW_BUILDER: {
          return new Builder();
        }
        case BUILD_MESSAGE_INFO: {
            java.lang.Object[] objects = new java.lang.Object[] {
              "value_",
            };
            java.lang.String info =
                "\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001";
            return newMessageInfo(DEFAULT_INSTANCE, info, objects);
        }
        // fall through
        case GET_DEFAULT_INSTANCE: {
          return DEFAULT_INSTANCE;
        }
        case GET_PARSER: {
          com.google.protobuf.Parser<com.presage.physiology.proto.MetricsProto.Strict> parser = PARSER;
          if (parser == null) {
            synchronized (com.presage.physiology.proto.MetricsProto.Strict.class) {
              parser = PARSER;
              if (parser == null) {
                parser =
                    new DefaultInstanceBasedParser<com.presage.physiology.proto.MetricsProto.Strict>(
                        DEFAULT_INSTANCE);
                PARSER = parser;
              }
            }
          }
          return parser;
      }
      case GET_MEMOIZED_IS_INITIALIZED: {
        return (byte) 1;
      }
      case SET_MEMOIZED_IS_INITIALIZED: {
        return null;
      }
      }
      throw new UnsupportedOperationException();
    }


    // @@protoc_insertion_point(class_scope:presage.physiology.Strict)
    private static final com.presage.physiology.proto.MetricsProto.Strict DEFAULT_INSTANCE;
    static {
      Strict defaultInstance = new Strict();
      // New instances are implicitly immutable so no need to make
      // immutable.
      DEFAULT_INSTANCE = defaultInstance;
      com.google.protobuf.GeneratedMessageLite.registerDefaultInstance(
        Strict.class, defaultInstance);
    }

    public static com.presage.physiology.proto.MetricsProto.Strict getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static volatile com.google.protobuf.Parser<Strict> PARSER;

    public static com.google.protobuf.Parser<Strict> parser() {
      return DEFAULT_INSTANCE.getParserForType();
    }
  }

  public interface PulseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:presage.physiology.Pulse)
      com.google.protobuf.MessageLiteOrBuilder {

    /**
     * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
     */
    java.util.List<com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence> 
        getRateList();
    /**
     * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
     */
    com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence getRate(int index);
    /**
     * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
     */
    int getRateCount();

    /**
     * <code>repeated .presage.physiology.Measurement trace = 2;</code>
     */
    java.util.List<com.presage.physiology.proto.MetricsProto.Measurement> 
        getTraceList();
    /**
     * <code>repeated .presage.physiology.Measurement trace = 2;</code>
     */
    com.presage.physiology.proto.MetricsProto.Measurement getTrace(int index);
    /**
     * <code>repeated .presage.physiology.Measurement trace = 2;</code>
     */
    int getTraceCount();

    /**
     * <code>repeated .presage.physiology.Measurement pulse_respiration_quotient = 3;</code>
     */
    java.util.List<com.presage.physiology.proto.MetricsProto.Measurement> 
        getPulseRespirationQuotientList();
    /**
     * <code>repeated .presage.physiology.Measurement pulse_respiration_quotient = 3;</code>
     */
    com.presage.physiology.proto.MetricsProto.Measurement getPulseRespirationQuotient(int index);
    /**
     * <code>repeated .presage.physiology.Measurement pulse_respiration_quotient = 3;</code>
     */
    int getPulseRespirationQuotientCount();

    /**
     * <code>.presage.physiology.Strict strict = 4;</code>
     * @return Whether the strict field is set.
     */
    boolean hasStrict();
    /**
     * <code>.presage.physiology.Strict strict = 4;</code>
     * @return The strict.
     */
    com.presage.physiology.proto.MetricsProto.Strict getStrict();
  }
  /**
   * Protobuf type {@code presage.physiology.Pulse}
   */
  public  static final class Pulse extends
      com.google.protobuf.GeneratedMessageLite<
          Pulse, Pulse.Builder> implements
      // @@protoc_insertion_point(message_implements:presage.physiology.Pulse)
      PulseOrBuilder {
    private Pulse() {
      rate_ = emptyProtobufList();
      trace_ = emptyProtobufList();
      pulseRespirationQuotient_ = emptyProtobufList();
    }
    public static final int RATE_FIELD_NUMBER = 1;
    private com.google.protobuf.Internal.ProtobufList<com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence> rate_;
    /**
     * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence> getRateList() {
      return rate_;
    }
    /**
     * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
     */
    public java.util.List<? extends com.presage.physiology.proto.MetricsProto.MeasurementWithConfidenceOrBuilder> 
        getRateOrBuilderList() {
      return rate_;
    }
    /**
     * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
     */
    @java.lang.Override
    public int getRateCount() {
      return rate_.size();
    }
    /**
     * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
     */
    @java.lang.Override
    public com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence getRate(int index) {
      return rate_.get(index);
    }
    /**
     * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
     */
    public com.presage.physiology.proto.MetricsProto.MeasurementWithConfidenceOrBuilder getRateOrBuilder(
        int index) {
      return rate_.get(index);
    }
    private void ensureRateIsMutable() {
      com.google.protobuf.Internal.ProtobufList<com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence> tmp = rate_;
      if (!tmp.isModifiable()) {
        rate_ =
            com.google.protobuf.GeneratedMessageLite.mutableCopy(tmp);
       }
    }

    /**
     * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
     */
    private void setRate(
        int index, com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence value) {
      value.getClass();
  ensureRateIsMutable();
      rate_.set(index, value);
    }
    /**
     * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
     */
    private void addRate(com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence value) {
      value.getClass();
  ensureRateIsMutable();
      rate_.add(value);
    }
    /**
     * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
     */
    private void addRate(
        int index, com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence value) {
      value.getClass();
  ensureRateIsMutable();
      rate_.add(index, value);
    }
    /**
     * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
     */
    private void addAllRate(
        java.lang.Iterable<? extends com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence> values) {
      ensureRateIsMutable();
      com.google.protobuf.AbstractMessageLite.addAll(
          values, rate_);
    }
    /**
     * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
     */
    private void clearRate() {
      rate_ = emptyProtobufList();
    }
    /**
     * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
     */
    private void removeRate(int index) {
      ensureRateIsMutable();
      rate_.remove(index);
    }

    public static final int TRACE_FIELD_NUMBER = 2;
    private com.google.protobuf.Internal.ProtobufList<com.presage.physiology.proto.MetricsProto.Measurement> trace_;
    /**
     * <code>repeated .presage.physiology.Measurement trace = 2;</code>
     */
    @java.lang.Override
    public java.util.List<com.presage.physiology.proto.MetricsProto.Measurement> getTraceList() {
      return trace_;
    }
    /**
     * <code>repeated .presage.physiology.Measurement trace = 2;</code>
     */
    public java.util.List<? extends com.presage.physiology.proto.MetricsProto.MeasurementOrBuilder> 
        getTraceOrBuilderList() {
      return trace_;
    }
    /**
     * <code>repeated .presage.physiology.Measurement trace = 2;</code>
     */
    @java.lang.Override
    public int getTraceCount() {
      return trace_.size();
    }
    /**
     * <code>repeated .presage.physiology.Measurement trace = 2;</code>
     */
    @java.lang.Override
    public com.presage.physiology.proto.MetricsProto.Measurement getTrace(int index) {
      return trace_.get(index);
    }
    /**
     * <code>repeated .presage.physiology.Measurement trace = 2;</code>
     */
    public com.presage.physiology.proto.MetricsProto.MeasurementOrBuilder getTraceOrBuilder(
        int index) {
      return trace_.get(index);
    }
    private void ensureTraceIsMutable() {
      com.google.protobuf.Internal.ProtobufList<com.presage.physiology.proto.MetricsProto.Measurement> tmp = trace_;
      if (!tmp.isModifiable()) {
        trace_ =
            com.google.protobuf.GeneratedMessageLite.mutableCopy(tmp);
       }
    }

    /**
     * <code>repeated .presage.physiology.Measurement trace = 2;</code>
     */
    private void setTrace(
        int index, com.presage.physiology.proto.MetricsProto.Measurement value) {
      value.getClass();
  ensureTraceIsMutable();
      trace_.set(index, value);
    }
    /**
     * <code>repeated .presage.physiology.Measurement trace = 2;</code>
     */
    private void addTrace(com.presage.physiology.proto.MetricsProto.Measurement value) {
      value.getClass();
  ensureTraceIsMutable();
      trace_.add(value);
    }
    /**
     * <code>repeated .presage.physiology.Measurement trace = 2;</code>
     */
    private void addTrace(
        int index, com.presage.physiology.proto.MetricsProto.Measurement value) {
      value.getClass();
  ensureTraceIsMutable();
      trace_.add(index, value);
    }
    /**
     * <code>repeated .presage.physiology.Measurement trace = 2;</code>
     */
    private void addAllTrace(
        java.lang.Iterable<? extends com.presage.physiology.proto.MetricsProto.Measurement> values) {
      ensureTraceIsMutable();
      com.google.protobuf.AbstractMessageLite.addAll(
          values, trace_);
    }
    /**
     * <code>repeated .presage.physiology.Measurement trace = 2;</code>
     */
    private void clearTrace() {
      trace_ = emptyProtobufList();
    }
    /**
     * <code>repeated .presage.physiology.Measurement trace = 2;</code>
     */
    private void removeTrace(int index) {
      ensureTraceIsMutable();
      trace_.remove(index);
    }

    public static final int PULSE_RESPIRATION_QUOTIENT_FIELD_NUMBER = 3;
    private com.google.protobuf.Internal.ProtobufList<com.presage.physiology.proto.MetricsProto.Measurement> pulseRespirationQuotient_;
    /**
     * <code>repeated .presage.physiology.Measurement pulse_respiration_quotient = 3;</code>
     */
    @java.lang.Override
    public java.util.List<com.presage.physiology.proto.MetricsProto.Measurement> getPulseRespirationQuotientList() {
      return pulseRespirationQuotient_;
    }
    /**
     * <code>repeated .presage.physiology.Measurement pulse_respiration_quotient = 3;</code>
     */
    public java.util.List<? extends com.presage.physiology.proto.MetricsProto.MeasurementOrBuilder> 
        getPulseRespirationQuotientOrBuilderList() {
      return pulseRespirationQuotient_;
    }
    /**
     * <code>repeated .presage.physiology.Measurement pulse_respiration_quotient = 3;</code>
     */
    @java.lang.Override
    public int getPulseRespirationQuotientCount() {
      return pulseRespirationQuotient_.size();
    }
    /**
     * <code>repeated .presage.physiology.Measurement pulse_respiration_quotient = 3;</code>
     */
    @java.lang.Override
    public com.presage.physiology.proto.MetricsProto.Measurement getPulseRespirationQuotient(int index) {
      return pulseRespirationQuotient_.get(index);
    }
    /**
     * <code>repeated .presage.physiology.Measurement pulse_respiration_quotient = 3;</code>
     */
    public com.presage.physiology.proto.MetricsProto.MeasurementOrBuilder getPulseRespirationQuotientOrBuilder(
        int index) {
      return pulseRespirationQuotient_.get(index);
    }
    private void ensurePulseRespirationQuotientIsMutable() {
      com.google.protobuf.Internal.ProtobufList<com.presage.physiology.proto.MetricsProto.Measurement> tmp = pulseRespirationQuotient_;
      if (!tmp.isModifiable()) {
        pulseRespirationQuotient_ =
            com.google.protobuf.GeneratedMessageLite.mutableCopy(tmp);
       }
    }

    /**
     * <code>repeated .presage.physiology.Measurement pulse_respiration_quotient = 3;</code>
     */
    private void setPulseRespirationQuotient(
        int index, com.presage.physiology.proto.MetricsProto.Measurement value) {
      value.getClass();
  ensurePulseRespirationQuotientIsMutable();
      pulseRespirationQuotient_.set(index, value);
    }
    /**
     * <code>repeated .presage.physiology.Measurement pulse_respiration_quotient = 3;</code>
     */
    private void addPulseRespirationQuotient(com.presage.physiology.proto.MetricsProto.Measurement value) {
      value.getClass();
  ensurePulseRespirationQuotientIsMutable();
      pulseRespirationQuotient_.add(value);
    }
    /**
     * <code>repeated .presage.physiology.Measurement pulse_respiration_quotient = 3;</code>
     */
    private void addPulseRespirationQuotient(
        int index, com.presage.physiology.proto.MetricsProto.Measurement value) {
      value.getClass();
  ensurePulseRespirationQuotientIsMutable();
      pulseRespirationQuotient_.add(index, value);
    }
    /**
     * <code>repeated .presage.physiology.Measurement pulse_respiration_quotient = 3;</code>
     */
    private void addAllPulseRespirationQuotient(
        java.lang.Iterable<? extends com.presage.physiology.proto.MetricsProto.Measurement> values) {
      ensurePulseRespirationQuotientIsMutable();
      com.google.protobuf.AbstractMessageLite.addAll(
          values, pulseRespirationQuotient_);
    }
    /**
     * <code>repeated .presage.physiology.Measurement pulse_respiration_quotient = 3;</code>
     */
    private void clearPulseRespirationQuotient() {
      pulseRespirationQuotient_ = emptyProtobufList();
    }
    /**
     * <code>repeated .presage.physiology.Measurement pulse_respiration_quotient = 3;</code>
     */
    private void removePulseRespirationQuotient(int index) {
      ensurePulseRespirationQuotientIsMutable();
      pulseRespirationQuotient_.remove(index);
    }

    public static final int STRICT_FIELD_NUMBER = 4;
    private com.presage.physiology.proto.MetricsProto.Strict strict_;
    /**
     * <code>.presage.physiology.Strict strict = 4;</code>
     */
    @java.lang.Override
    public boolean hasStrict() {
      return strict_ != null;
    }
    /**
     * <code>.presage.physiology.Strict strict = 4;</code>
     */
    @java.lang.Override
    public com.presage.physiology.proto.MetricsProto.Strict getStrict() {
      return strict_ == null ? com.presage.physiology.proto.MetricsProto.Strict.getDefaultInstance() : strict_;
    }
    /**
     * <code>.presage.physiology.Strict strict = 4;</code>
     */
    private void setStrict(com.presage.physiology.proto.MetricsProto.Strict value) {
      value.getClass();
  strict_ = value;
      
      }
    /**
     * <code>.presage.physiology.Strict strict = 4;</code>
     */
    @java.lang.SuppressWarnings({"ReferenceEquality"})
    private void mergeStrict(com.presage.physiology.proto.MetricsProto.Strict value) {
      value.getClass();
  if (strict_ != null &&
          strict_ != com.presage.physiology.proto.MetricsProto.Strict.getDefaultInstance()) {
        strict_ =
          com.presage.physiology.proto.MetricsProto.Strict.newBuilder(strict_).mergeFrom(value).buildPartial();
      } else {
        strict_ = value;
      }
      
    }
    /**
     * <code>.presage.physiology.Strict strict = 4;</code>
     */
    private void clearStrict() {  strict_ = null;
      
    }

    public static com.presage.physiology.proto.MetricsProto.Pulse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.MetricsProto.Pulse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.Pulse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.MetricsProto.Pulse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.Pulse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.MetricsProto.Pulse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.Pulse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.MetricsProto.Pulse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.Pulse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.MetricsProto.Pulse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.Pulse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.MetricsProto.Pulse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }

    public static Builder newBuilder() {
      return (Builder) DEFAULT_INSTANCE.createBuilder();
    }
    public static Builder newBuilder(com.presage.physiology.proto.MetricsProto.Pulse prototype) {
      return (Builder) DEFAULT_INSTANCE.createBuilder(prototype);
    }

    /**
     * Protobuf type {@code presage.physiology.Pulse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageLite.Builder<
          com.presage.physiology.proto.MetricsProto.Pulse, Builder> implements
        // @@protoc_insertion_point(builder_implements:presage.physiology.Pulse)
        com.presage.physiology.proto.MetricsProto.PulseOrBuilder {
      // Construct using com.presage.physiology.proto.MetricsProto.Pulse.newBuilder()
      private Builder() {
        super(DEFAULT_INSTANCE);
      }


      /**
       * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
       */
      @java.lang.Override
      public java.util.List<com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence> getRateList() {
        return java.util.Collections.unmodifiableList(
            instance.getRateList());
      }
      /**
       * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
       */
      @java.lang.Override
      public int getRateCount() {
        return instance.getRateCount();
      }/**
       * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
       */
      @java.lang.Override
      public com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence getRate(int index) {
        return instance.getRate(index);
      }
      /**
       * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
       */
      public Builder setRate(
          int index, com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence value) {
        copyOnWrite();
        instance.setRate(index, value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
       */
      public Builder setRate(
          int index, com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence.Builder builderForValue) {
        copyOnWrite();
        instance.setRate(index,
            builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
       */
      public Builder addRate(com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence value) {
        copyOnWrite();
        instance.addRate(value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
       */
      public Builder addRate(
          int index, com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence value) {
        copyOnWrite();
        instance.addRate(index, value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
       */
      public Builder addRate(
          com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence.Builder builderForValue) {
        copyOnWrite();
        instance.addRate(builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
       */
      public Builder addRate(
          int index, com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence.Builder builderForValue) {
        copyOnWrite();
        instance.addRate(index,
            builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
       */
      public Builder addAllRate(
          java.lang.Iterable<? extends com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence> values) {
        copyOnWrite();
        instance.addAllRate(values);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
       */
      public Builder clearRate() {
        copyOnWrite();
        instance.clearRate();
        return this;
      }
      /**
       * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
       */
      public Builder removeRate(int index) {
        copyOnWrite();
        instance.removeRate(index);
        return this;
      }

      /**
       * <code>repeated .presage.physiology.Measurement trace = 2;</code>
       */
      @java.lang.Override
      public java.util.List<com.presage.physiology.proto.MetricsProto.Measurement> getTraceList() {
        return java.util.Collections.unmodifiableList(
            instance.getTraceList());
      }
      /**
       * <code>repeated .presage.physiology.Measurement trace = 2;</code>
       */
      @java.lang.Override
      public int getTraceCount() {
        return instance.getTraceCount();
      }/**
       * <code>repeated .presage.physiology.Measurement trace = 2;</code>
       */
      @java.lang.Override
      public com.presage.physiology.proto.MetricsProto.Measurement getTrace(int index) {
        return instance.getTrace(index);
      }
      /**
       * <code>repeated .presage.physiology.Measurement trace = 2;</code>
       */
      public Builder setTrace(
          int index, com.presage.physiology.proto.MetricsProto.Measurement value) {
        copyOnWrite();
        instance.setTrace(index, value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement trace = 2;</code>
       */
      public Builder setTrace(
          int index, com.presage.physiology.proto.MetricsProto.Measurement.Builder builderForValue) {
        copyOnWrite();
        instance.setTrace(index,
            builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement trace = 2;</code>
       */
      public Builder addTrace(com.presage.physiology.proto.MetricsProto.Measurement value) {
        copyOnWrite();
        instance.addTrace(value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement trace = 2;</code>
       */
      public Builder addTrace(
          int index, com.presage.physiology.proto.MetricsProto.Measurement value) {
        copyOnWrite();
        instance.addTrace(index, value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement trace = 2;</code>
       */
      public Builder addTrace(
          com.presage.physiology.proto.MetricsProto.Measurement.Builder builderForValue) {
        copyOnWrite();
        instance.addTrace(builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement trace = 2;</code>
       */
      public Builder addTrace(
          int index, com.presage.physiology.proto.MetricsProto.Measurement.Builder builderForValue) {
        copyOnWrite();
        instance.addTrace(index,
            builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement trace = 2;</code>
       */
      public Builder addAllTrace(
          java.lang.Iterable<? extends com.presage.physiology.proto.MetricsProto.Measurement> values) {
        copyOnWrite();
        instance.addAllTrace(values);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement trace = 2;</code>
       */
      public Builder clearTrace() {
        copyOnWrite();
        instance.clearTrace();
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement trace = 2;</code>
       */
      public Builder removeTrace(int index) {
        copyOnWrite();
        instance.removeTrace(index);
        return this;
      }

      /**
       * <code>repeated .presage.physiology.Measurement pulse_respiration_quotient = 3;</code>
       */
      @java.lang.Override
      public java.util.List<com.presage.physiology.proto.MetricsProto.Measurement> getPulseRespirationQuotientList() {
        return java.util.Collections.unmodifiableList(
            instance.getPulseRespirationQuotientList());
      }
      /**
       * <code>repeated .presage.physiology.Measurement pulse_respiration_quotient = 3;</code>
       */
      @java.lang.Override
      public int getPulseRespirationQuotientCount() {
        return instance.getPulseRespirationQuotientCount();
      }/**
       * <code>repeated .presage.physiology.Measurement pulse_respiration_quotient = 3;</code>
       */
      @java.lang.Override
      public com.presage.physiology.proto.MetricsProto.Measurement getPulseRespirationQuotient(int index) {
        return instance.getPulseRespirationQuotient(index);
      }
      /**
       * <code>repeated .presage.physiology.Measurement pulse_respiration_quotient = 3;</code>
       */
      public Builder setPulseRespirationQuotient(
          int index, com.presage.physiology.proto.MetricsProto.Measurement value) {
        copyOnWrite();
        instance.setPulseRespirationQuotient(index, value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement pulse_respiration_quotient = 3;</code>
       */
      public Builder setPulseRespirationQuotient(
          int index, com.presage.physiology.proto.MetricsProto.Measurement.Builder builderForValue) {
        copyOnWrite();
        instance.setPulseRespirationQuotient(index,
            builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement pulse_respiration_quotient = 3;</code>
       */
      public Builder addPulseRespirationQuotient(com.presage.physiology.proto.MetricsProto.Measurement value) {
        copyOnWrite();
        instance.addPulseRespirationQuotient(value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement pulse_respiration_quotient = 3;</code>
       */
      public Builder addPulseRespirationQuotient(
          int index, com.presage.physiology.proto.MetricsProto.Measurement value) {
        copyOnWrite();
        instance.addPulseRespirationQuotient(index, value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement pulse_respiration_quotient = 3;</code>
       */
      public Builder addPulseRespirationQuotient(
          com.presage.physiology.proto.MetricsProto.Measurement.Builder builderForValue) {
        copyOnWrite();
        instance.addPulseRespirationQuotient(builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement pulse_respiration_quotient = 3;</code>
       */
      public Builder addPulseRespirationQuotient(
          int index, com.presage.physiology.proto.MetricsProto.Measurement.Builder builderForValue) {
        copyOnWrite();
        instance.addPulseRespirationQuotient(index,
            builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement pulse_respiration_quotient = 3;</code>
       */
      public Builder addAllPulseRespirationQuotient(
          java.lang.Iterable<? extends com.presage.physiology.proto.MetricsProto.Measurement> values) {
        copyOnWrite();
        instance.addAllPulseRespirationQuotient(values);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement pulse_respiration_quotient = 3;</code>
       */
      public Builder clearPulseRespirationQuotient() {
        copyOnWrite();
        instance.clearPulseRespirationQuotient();
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement pulse_respiration_quotient = 3;</code>
       */
      public Builder removePulseRespirationQuotient(int index) {
        copyOnWrite();
        instance.removePulseRespirationQuotient(index);
        return this;
      }

      /**
       * <code>.presage.physiology.Strict strict = 4;</code>
       */
      @java.lang.Override
      public boolean hasStrict() {
        return instance.hasStrict();
      }
      /**
       * <code>.presage.physiology.Strict strict = 4;</code>
       */
      @java.lang.Override
      public com.presage.physiology.proto.MetricsProto.Strict getStrict() {
        return instance.getStrict();
      }
      /**
       * <code>.presage.physiology.Strict strict = 4;</code>
       */
      public Builder setStrict(com.presage.physiology.proto.MetricsProto.Strict value) {
        copyOnWrite();
        instance.setStrict(value);
        return this;
        }
      /**
       * <code>.presage.physiology.Strict strict = 4;</code>
       */
      public Builder setStrict(
          com.presage.physiology.proto.MetricsProto.Strict.Builder builderForValue) {
        copyOnWrite();
        instance.setStrict(builderForValue.build());
        return this;
      }
      /**
       * <code>.presage.physiology.Strict strict = 4;</code>
       */
      public Builder mergeStrict(com.presage.physiology.proto.MetricsProto.Strict value) {
        copyOnWrite();
        instance.mergeStrict(value);
        return this;
      }
      /**
       * <code>.presage.physiology.Strict strict = 4;</code>
       */
      public Builder clearStrict() {  copyOnWrite();
        instance.clearStrict();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:presage.physiology.Pulse)
    }
    @java.lang.Override
    @java.lang.SuppressWarnings({"unchecked", "fallthrough"})
    protected final java.lang.Object dynamicMethod(
        com.google.protobuf.GeneratedMessageLite.MethodToInvoke method,
        java.lang.Object arg0, java.lang.Object arg1) {
      switch (method) {
        case NEW_MUTABLE_INSTANCE: {
          return new com.presage.physiology.proto.MetricsProto.Pulse();
        }
        case NEW_BUILDER: {
          return new Builder();
        }
        case BUILD_MESSAGE_INFO: {
            java.lang.Object[] objects = new java.lang.Object[] {
              "rate_",
              com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence.class,
              "trace_",
              com.presage.physiology.proto.MetricsProto.Measurement.class,
              "pulseRespirationQuotient_",
              com.presage.physiology.proto.MetricsProto.Measurement.class,
              "strict_",
            };
            java.lang.String info =
                "\u0000\u0004\u0000\u0000\u0001\u0004\u0004\u0000\u0003\u0000\u0001\u001b\u0002\u001b" +
                "\u0003\u001b\u0004\t";
            return newMessageInfo(DEFAULT_INSTANCE, info, objects);
        }
        // fall through
        case GET_DEFAULT_INSTANCE: {
          return DEFAULT_INSTANCE;
        }
        case GET_PARSER: {
          com.google.protobuf.Parser<com.presage.physiology.proto.MetricsProto.Pulse> parser = PARSER;
          if (parser == null) {
            synchronized (com.presage.physiology.proto.MetricsProto.Pulse.class) {
              parser = PARSER;
              if (parser == null) {
                parser =
                    new DefaultInstanceBasedParser<com.presage.physiology.proto.MetricsProto.Pulse>(
                        DEFAULT_INSTANCE);
                PARSER = parser;
              }
            }
          }
          return parser;
      }
      case GET_MEMOIZED_IS_INITIALIZED: {
        return (byte) 1;
      }
      case SET_MEMOIZED_IS_INITIALIZED: {
        return null;
      }
      }
      throw new UnsupportedOperationException();
    }


    // @@protoc_insertion_point(class_scope:presage.physiology.Pulse)
    private static final com.presage.physiology.proto.MetricsProto.Pulse DEFAULT_INSTANCE;
    static {
      Pulse defaultInstance = new Pulse();
      // New instances are implicitly immutable so no need to make
      // immutable.
      DEFAULT_INSTANCE = defaultInstance;
      com.google.protobuf.GeneratedMessageLite.registerDefaultInstance(
        Pulse.class, defaultInstance);
    }

    public static com.presage.physiology.proto.MetricsProto.Pulse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static volatile com.google.protobuf.Parser<Pulse> PARSER;

    public static com.google.protobuf.Parser<Pulse> parser() {
      return DEFAULT_INSTANCE.getParserForType();
    }
  }

  public interface TraceOrBuilder extends
      // @@protoc_insertion_point(interface_extends:presage.physiology.Trace)
      com.google.protobuf.MessageLiteOrBuilder {

    /**
     * <code>repeated .presage.physiology.Measurement sample = 1;</code>
     */
    java.util.List<com.presage.physiology.proto.MetricsProto.Measurement> 
        getSampleList();
    /**
     * <code>repeated .presage.physiology.Measurement sample = 1;</code>
     */
    com.presage.physiology.proto.MetricsProto.Measurement getSample(int index);
    /**
     * <code>repeated .presage.physiology.Measurement sample = 1;</code>
     */
    int getSampleCount();
  }
  /**
   * Protobuf type {@code presage.physiology.Trace}
   */
  public  static final class Trace extends
      com.google.protobuf.GeneratedMessageLite<
          Trace, Trace.Builder> implements
      // @@protoc_insertion_point(message_implements:presage.physiology.Trace)
      TraceOrBuilder {
    private Trace() {
      sample_ = emptyProtobufList();
    }
    public static final int SAMPLE_FIELD_NUMBER = 1;
    private com.google.protobuf.Internal.ProtobufList<com.presage.physiology.proto.MetricsProto.Measurement> sample_;
    /**
     * <code>repeated .presage.physiology.Measurement sample = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.presage.physiology.proto.MetricsProto.Measurement> getSampleList() {
      return sample_;
    }
    /**
     * <code>repeated .presage.physiology.Measurement sample = 1;</code>
     */
    public java.util.List<? extends com.presage.physiology.proto.MetricsProto.MeasurementOrBuilder> 
        getSampleOrBuilderList() {
      return sample_;
    }
    /**
     * <code>repeated .presage.physiology.Measurement sample = 1;</code>
     */
    @java.lang.Override
    public int getSampleCount() {
      return sample_.size();
    }
    /**
     * <code>repeated .presage.physiology.Measurement sample = 1;</code>
     */
    @java.lang.Override
    public com.presage.physiology.proto.MetricsProto.Measurement getSample(int index) {
      return sample_.get(index);
    }
    /**
     * <code>repeated .presage.physiology.Measurement sample = 1;</code>
     */
    public com.presage.physiology.proto.MetricsProto.MeasurementOrBuilder getSampleOrBuilder(
        int index) {
      return sample_.get(index);
    }
    private void ensureSampleIsMutable() {
      com.google.protobuf.Internal.ProtobufList<com.presage.physiology.proto.MetricsProto.Measurement> tmp = sample_;
      if (!tmp.isModifiable()) {
        sample_ =
            com.google.protobuf.GeneratedMessageLite.mutableCopy(tmp);
       }
    }

    /**
     * <code>repeated .presage.physiology.Measurement sample = 1;</code>
     */
    private void setSample(
        int index, com.presage.physiology.proto.MetricsProto.Measurement value) {
      value.getClass();
  ensureSampleIsMutable();
      sample_.set(index, value);
    }
    /**
     * <code>repeated .presage.physiology.Measurement sample = 1;</code>
     */
    private void addSample(com.presage.physiology.proto.MetricsProto.Measurement value) {
      value.getClass();
  ensureSampleIsMutable();
      sample_.add(value);
    }
    /**
     * <code>repeated .presage.physiology.Measurement sample = 1;</code>
     */
    private void addSample(
        int index, com.presage.physiology.proto.MetricsProto.Measurement value) {
      value.getClass();
  ensureSampleIsMutable();
      sample_.add(index, value);
    }
    /**
     * <code>repeated .presage.physiology.Measurement sample = 1;</code>
     */
    private void addAllSample(
        java.lang.Iterable<? extends com.presage.physiology.proto.MetricsProto.Measurement> values) {
      ensureSampleIsMutable();
      com.google.protobuf.AbstractMessageLite.addAll(
          values, sample_);
    }
    /**
     * <code>repeated .presage.physiology.Measurement sample = 1;</code>
     */
    private void clearSample() {
      sample_ = emptyProtobufList();
    }
    /**
     * <code>repeated .presage.physiology.Measurement sample = 1;</code>
     */
    private void removeSample(int index) {
      ensureSampleIsMutable();
      sample_.remove(index);
    }

    public static com.presage.physiology.proto.MetricsProto.Trace parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.MetricsProto.Trace parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.Trace parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.MetricsProto.Trace parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.Trace parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.MetricsProto.Trace parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.Trace parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.MetricsProto.Trace parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.Trace parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.MetricsProto.Trace parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.Trace parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.MetricsProto.Trace parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }

    public static Builder newBuilder() {
      return (Builder) DEFAULT_INSTANCE.createBuilder();
    }
    public static Builder newBuilder(com.presage.physiology.proto.MetricsProto.Trace prototype) {
      return (Builder) DEFAULT_INSTANCE.createBuilder(prototype);
    }

    /**
     * Protobuf type {@code presage.physiology.Trace}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageLite.Builder<
          com.presage.physiology.proto.MetricsProto.Trace, Builder> implements
        // @@protoc_insertion_point(builder_implements:presage.physiology.Trace)
        com.presage.physiology.proto.MetricsProto.TraceOrBuilder {
      // Construct using com.presage.physiology.proto.MetricsProto.Trace.newBuilder()
      private Builder() {
        super(DEFAULT_INSTANCE);
      }


      /**
       * <code>repeated .presage.physiology.Measurement sample = 1;</code>
       */
      @java.lang.Override
      public java.util.List<com.presage.physiology.proto.MetricsProto.Measurement> getSampleList() {
        return java.util.Collections.unmodifiableList(
            instance.getSampleList());
      }
      /**
       * <code>repeated .presage.physiology.Measurement sample = 1;</code>
       */
      @java.lang.Override
      public int getSampleCount() {
        return instance.getSampleCount();
      }/**
       * <code>repeated .presage.physiology.Measurement sample = 1;</code>
       */
      @java.lang.Override
      public com.presage.physiology.proto.MetricsProto.Measurement getSample(int index) {
        return instance.getSample(index);
      }
      /**
       * <code>repeated .presage.physiology.Measurement sample = 1;</code>
       */
      public Builder setSample(
          int index, com.presage.physiology.proto.MetricsProto.Measurement value) {
        copyOnWrite();
        instance.setSample(index, value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement sample = 1;</code>
       */
      public Builder setSample(
          int index, com.presage.physiology.proto.MetricsProto.Measurement.Builder builderForValue) {
        copyOnWrite();
        instance.setSample(index,
            builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement sample = 1;</code>
       */
      public Builder addSample(com.presage.physiology.proto.MetricsProto.Measurement value) {
        copyOnWrite();
        instance.addSample(value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement sample = 1;</code>
       */
      public Builder addSample(
          int index, com.presage.physiology.proto.MetricsProto.Measurement value) {
        copyOnWrite();
        instance.addSample(index, value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement sample = 1;</code>
       */
      public Builder addSample(
          com.presage.physiology.proto.MetricsProto.Measurement.Builder builderForValue) {
        copyOnWrite();
        instance.addSample(builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement sample = 1;</code>
       */
      public Builder addSample(
          int index, com.presage.physiology.proto.MetricsProto.Measurement.Builder builderForValue) {
        copyOnWrite();
        instance.addSample(index,
            builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement sample = 1;</code>
       */
      public Builder addAllSample(
          java.lang.Iterable<? extends com.presage.physiology.proto.MetricsProto.Measurement> values) {
        copyOnWrite();
        instance.addAllSample(values);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement sample = 1;</code>
       */
      public Builder clearSample() {
        copyOnWrite();
        instance.clearSample();
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement sample = 1;</code>
       */
      public Builder removeSample(int index) {
        copyOnWrite();
        instance.removeSample(index);
        return this;
      }

      // @@protoc_insertion_point(builder_scope:presage.physiology.Trace)
    }
    @java.lang.Override
    @java.lang.SuppressWarnings({"unchecked", "fallthrough"})
    protected final java.lang.Object dynamicMethod(
        com.google.protobuf.GeneratedMessageLite.MethodToInvoke method,
        java.lang.Object arg0, java.lang.Object arg1) {
      switch (method) {
        case NEW_MUTABLE_INSTANCE: {
          return new com.presage.physiology.proto.MetricsProto.Trace();
        }
        case NEW_BUILDER: {
          return new Builder();
        }
        case BUILD_MESSAGE_INFO: {
            java.lang.Object[] objects = new java.lang.Object[] {
              "sample_",
              com.presage.physiology.proto.MetricsProto.Measurement.class,
            };
            java.lang.String info =
                "\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u001b";
            return newMessageInfo(DEFAULT_INSTANCE, info, objects);
        }
        // fall through
        case GET_DEFAULT_INSTANCE: {
          return DEFAULT_INSTANCE;
        }
        case GET_PARSER: {
          com.google.protobuf.Parser<com.presage.physiology.proto.MetricsProto.Trace> parser = PARSER;
          if (parser == null) {
            synchronized (com.presage.physiology.proto.MetricsProto.Trace.class) {
              parser = PARSER;
              if (parser == null) {
                parser =
                    new DefaultInstanceBasedParser<com.presage.physiology.proto.MetricsProto.Trace>(
                        DEFAULT_INSTANCE);
                PARSER = parser;
              }
            }
          }
          return parser;
      }
      case GET_MEMOIZED_IS_INITIALIZED: {
        return (byte) 1;
      }
      case SET_MEMOIZED_IS_INITIALIZED: {
        return null;
      }
      }
      throw new UnsupportedOperationException();
    }


    // @@protoc_insertion_point(class_scope:presage.physiology.Trace)
    private static final com.presage.physiology.proto.MetricsProto.Trace DEFAULT_INSTANCE;
    static {
      Trace defaultInstance = new Trace();
      // New instances are implicitly immutable so no need to make
      // immutable.
      DEFAULT_INSTANCE = defaultInstance;
      com.google.protobuf.GeneratedMessageLite.registerDefaultInstance(
        Trace.class, defaultInstance);
    }

    public static com.presage.physiology.proto.MetricsProto.Trace getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static volatile com.google.protobuf.Parser<Trace> PARSER;

    public static com.google.protobuf.Parser<Trace> parser() {
      return DEFAULT_INSTANCE.getParserForType();
    }
  }

  public interface BreathingOrBuilder extends
      // @@protoc_insertion_point(interface_extends:presage.physiology.Breathing)
      com.google.protobuf.MessageLiteOrBuilder {

    /**
     * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
     */
    java.util.List<com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence> 
        getRateList();
    /**
     * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
     */
    com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence getRate(int index);
    /**
     * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
     */
    int getRateCount();

    /**
     * <code>repeated .presage.physiology.Measurement upper_trace = 2;</code>
     */
    java.util.List<com.presage.physiology.proto.MetricsProto.Measurement> 
        getUpperTraceList();
    /**
     * <code>repeated .presage.physiology.Measurement upper_trace = 2;</code>
     */
    com.presage.physiology.proto.MetricsProto.Measurement getUpperTrace(int index);
    /**
     * <code>repeated .presage.physiology.Measurement upper_trace = 2;</code>
     */
    int getUpperTraceCount();

    /**
     * <code>repeated .presage.physiology.Measurement lower_trace = 3;</code>
     */
    java.util.List<com.presage.physiology.proto.MetricsProto.Measurement> 
        getLowerTraceList();
    /**
     * <code>repeated .presage.physiology.Measurement lower_trace = 3;</code>
     */
    com.presage.physiology.proto.MetricsProto.Measurement getLowerTrace(int index);
    /**
     * <code>repeated .presage.physiology.Measurement lower_trace = 3;</code>
     */
    int getLowerTraceCount();

    /**
     * <code>repeated .presage.physiology.Measurement amplitude = 4;</code>
     */
    java.util.List<com.presage.physiology.proto.MetricsProto.Measurement> 
        getAmplitudeList();
    /**
     * <code>repeated .presage.physiology.Measurement amplitude = 4;</code>
     */
    com.presage.physiology.proto.MetricsProto.Measurement getAmplitude(int index);
    /**
     * <code>repeated .presage.physiology.Measurement amplitude = 4;</code>
     */
    int getAmplitudeCount();

    /**
     * <code>repeated .presage.physiology.DetectionStatus apnea = 5;</code>
     */
    java.util.List<com.presage.physiology.proto.MetricsProto.DetectionStatus> 
        getApneaList();
    /**
     * <code>repeated .presage.physiology.DetectionStatus apnea = 5;</code>
     */
    com.presage.physiology.proto.MetricsProto.DetectionStatus getApnea(int index);
    /**
     * <code>repeated .presage.physiology.DetectionStatus apnea = 5;</code>
     */
    int getApneaCount();

    /**
     * <code>repeated .presage.physiology.Measurement respiratory_line_length = 6;</code>
     */
    java.util.List<com.presage.physiology.proto.MetricsProto.Measurement> 
        getRespiratoryLineLengthList();
    /**
     * <code>repeated .presage.physiology.Measurement respiratory_line_length = 6;</code>
     */
    com.presage.physiology.proto.MetricsProto.Measurement getRespiratoryLineLength(int index);
    /**
     * <code>repeated .presage.physiology.Measurement respiratory_line_length = 6;</code>
     */
    int getRespiratoryLineLengthCount();

    /**
     * <code>repeated .presage.physiology.Measurement baseline = 7;</code>
     */
    java.util.List<com.presage.physiology.proto.MetricsProto.Measurement> 
        getBaselineList();
    /**
     * <code>repeated .presage.physiology.Measurement baseline = 7;</code>
     */
    com.presage.physiology.proto.MetricsProto.Measurement getBaseline(int index);
    /**
     * <code>repeated .presage.physiology.Measurement baseline = 7;</code>
     */
    int getBaselineCount();

    /**
     * <code>repeated .presage.physiology.Measurement inhale_exhale_ratio = 8;</code>
     */
    java.util.List<com.presage.physiology.proto.MetricsProto.Measurement> 
        getInhaleExhaleRatioList();
    /**
     * <code>repeated .presage.physiology.Measurement inhale_exhale_ratio = 8;</code>
     */
    com.presage.physiology.proto.MetricsProto.Measurement getInhaleExhaleRatio(int index);
    /**
     * <code>repeated .presage.physiology.Measurement inhale_exhale_ratio = 8;</code>
     */
    int getInhaleExhaleRatioCount();

    /**
     * <code>.presage.physiology.Strict strict = 9;</code>
     * @return Whether the strict field is set.
     */
    boolean hasStrict();
    /**
     * <code>.presage.physiology.Strict strict = 9;</code>
     * @return The strict.
     */
    com.presage.physiology.proto.MetricsProto.Strict getStrict();
  }
  /**
   * Protobuf type {@code presage.physiology.Breathing}
   */
  public  static final class Breathing extends
      com.google.protobuf.GeneratedMessageLite<
          Breathing, Breathing.Builder> implements
      // @@protoc_insertion_point(message_implements:presage.physiology.Breathing)
      BreathingOrBuilder {
    private Breathing() {
      rate_ = emptyProtobufList();
      upperTrace_ = emptyProtobufList();
      lowerTrace_ = emptyProtobufList();
      amplitude_ = emptyProtobufList();
      apnea_ = emptyProtobufList();
      respiratoryLineLength_ = emptyProtobufList();
      baseline_ = emptyProtobufList();
      inhaleExhaleRatio_ = emptyProtobufList();
    }
    public static final int RATE_FIELD_NUMBER = 1;
    private com.google.protobuf.Internal.ProtobufList<com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence> rate_;
    /**
     * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence> getRateList() {
      return rate_;
    }
    /**
     * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
     */
    public java.util.List<? extends com.presage.physiology.proto.MetricsProto.MeasurementWithConfidenceOrBuilder> 
        getRateOrBuilderList() {
      return rate_;
    }
    /**
     * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
     */
    @java.lang.Override
    public int getRateCount() {
      return rate_.size();
    }
    /**
     * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
     */
    @java.lang.Override
    public com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence getRate(int index) {
      return rate_.get(index);
    }
    /**
     * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
     */
    public com.presage.physiology.proto.MetricsProto.MeasurementWithConfidenceOrBuilder getRateOrBuilder(
        int index) {
      return rate_.get(index);
    }
    private void ensureRateIsMutable() {
      com.google.protobuf.Internal.ProtobufList<com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence> tmp = rate_;
      if (!tmp.isModifiable()) {
        rate_ =
            com.google.protobuf.GeneratedMessageLite.mutableCopy(tmp);
       }
    }

    /**
     * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
     */
    private void setRate(
        int index, com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence value) {
      value.getClass();
  ensureRateIsMutable();
      rate_.set(index, value);
    }
    /**
     * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
     */
    private void addRate(com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence value) {
      value.getClass();
  ensureRateIsMutable();
      rate_.add(value);
    }
    /**
     * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
     */
    private void addRate(
        int index, com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence value) {
      value.getClass();
  ensureRateIsMutable();
      rate_.add(index, value);
    }
    /**
     * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
     */
    private void addAllRate(
        java.lang.Iterable<? extends com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence> values) {
      ensureRateIsMutable();
      com.google.protobuf.AbstractMessageLite.addAll(
          values, rate_);
    }
    /**
     * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
     */
    private void clearRate() {
      rate_ = emptyProtobufList();
    }
    /**
     * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
     */
    private void removeRate(int index) {
      ensureRateIsMutable();
      rate_.remove(index);
    }

    public static final int UPPER_TRACE_FIELD_NUMBER = 2;
    private com.google.protobuf.Internal.ProtobufList<com.presage.physiology.proto.MetricsProto.Measurement> upperTrace_;
    /**
     * <code>repeated .presage.physiology.Measurement upper_trace = 2;</code>
     */
    @java.lang.Override
    public java.util.List<com.presage.physiology.proto.MetricsProto.Measurement> getUpperTraceList() {
      return upperTrace_;
    }
    /**
     * <code>repeated .presage.physiology.Measurement upper_trace = 2;</code>
     */
    public java.util.List<? extends com.presage.physiology.proto.MetricsProto.MeasurementOrBuilder> 
        getUpperTraceOrBuilderList() {
      return upperTrace_;
    }
    /**
     * <code>repeated .presage.physiology.Measurement upper_trace = 2;</code>
     */
    @java.lang.Override
    public int getUpperTraceCount() {
      return upperTrace_.size();
    }
    /**
     * <code>repeated .presage.physiology.Measurement upper_trace = 2;</code>
     */
    @java.lang.Override
    public com.presage.physiology.proto.MetricsProto.Measurement getUpperTrace(int index) {
      return upperTrace_.get(index);
    }
    /**
     * <code>repeated .presage.physiology.Measurement upper_trace = 2;</code>
     */
    public com.presage.physiology.proto.MetricsProto.MeasurementOrBuilder getUpperTraceOrBuilder(
        int index) {
      return upperTrace_.get(index);
    }
    private void ensureUpperTraceIsMutable() {
      com.google.protobuf.Internal.ProtobufList<com.presage.physiology.proto.MetricsProto.Measurement> tmp = upperTrace_;
      if (!tmp.isModifiable()) {
        upperTrace_ =
            com.google.protobuf.GeneratedMessageLite.mutableCopy(tmp);
       }
    }

    /**
     * <code>repeated .presage.physiology.Measurement upper_trace = 2;</code>
     */
    private void setUpperTrace(
        int index, com.presage.physiology.proto.MetricsProto.Measurement value) {
      value.getClass();
  ensureUpperTraceIsMutable();
      upperTrace_.set(index, value);
    }
    /**
     * <code>repeated .presage.physiology.Measurement upper_trace = 2;</code>
     */
    private void addUpperTrace(com.presage.physiology.proto.MetricsProto.Measurement value) {
      value.getClass();
  ensureUpperTraceIsMutable();
      upperTrace_.add(value);
    }
    /**
     * <code>repeated .presage.physiology.Measurement upper_trace = 2;</code>
     */
    private void addUpperTrace(
        int index, com.presage.physiology.proto.MetricsProto.Measurement value) {
      value.getClass();
  ensureUpperTraceIsMutable();
      upperTrace_.add(index, value);
    }
    /**
     * <code>repeated .presage.physiology.Measurement upper_trace = 2;</code>
     */
    private void addAllUpperTrace(
        java.lang.Iterable<? extends com.presage.physiology.proto.MetricsProto.Measurement> values) {
      ensureUpperTraceIsMutable();
      com.google.protobuf.AbstractMessageLite.addAll(
          values, upperTrace_);
    }
    /**
     * <code>repeated .presage.physiology.Measurement upper_trace = 2;</code>
     */
    private void clearUpperTrace() {
      upperTrace_ = emptyProtobufList();
    }
    /**
     * <code>repeated .presage.physiology.Measurement upper_trace = 2;</code>
     */
    private void removeUpperTrace(int index) {
      ensureUpperTraceIsMutable();
      upperTrace_.remove(index);
    }

    public static final int LOWER_TRACE_FIELD_NUMBER = 3;
    private com.google.protobuf.Internal.ProtobufList<com.presage.physiology.proto.MetricsProto.Measurement> lowerTrace_;
    /**
     * <code>repeated .presage.physiology.Measurement lower_trace = 3;</code>
     */
    @java.lang.Override
    public java.util.List<com.presage.physiology.proto.MetricsProto.Measurement> getLowerTraceList() {
      return lowerTrace_;
    }
    /**
     * <code>repeated .presage.physiology.Measurement lower_trace = 3;</code>
     */
    public java.util.List<? extends com.presage.physiology.proto.MetricsProto.MeasurementOrBuilder> 
        getLowerTraceOrBuilderList() {
      return lowerTrace_;
    }
    /**
     * <code>repeated .presage.physiology.Measurement lower_trace = 3;</code>
     */
    @java.lang.Override
    public int getLowerTraceCount() {
      return lowerTrace_.size();
    }
    /**
     * <code>repeated .presage.physiology.Measurement lower_trace = 3;</code>
     */
    @java.lang.Override
    public com.presage.physiology.proto.MetricsProto.Measurement getLowerTrace(int index) {
      return lowerTrace_.get(index);
    }
    /**
     * <code>repeated .presage.physiology.Measurement lower_trace = 3;</code>
     */
    public com.presage.physiology.proto.MetricsProto.MeasurementOrBuilder getLowerTraceOrBuilder(
        int index) {
      return lowerTrace_.get(index);
    }
    private void ensureLowerTraceIsMutable() {
      com.google.protobuf.Internal.ProtobufList<com.presage.physiology.proto.MetricsProto.Measurement> tmp = lowerTrace_;
      if (!tmp.isModifiable()) {
        lowerTrace_ =
            com.google.protobuf.GeneratedMessageLite.mutableCopy(tmp);
       }
    }

    /**
     * <code>repeated .presage.physiology.Measurement lower_trace = 3;</code>
     */
    private void setLowerTrace(
        int index, com.presage.physiology.proto.MetricsProto.Measurement value) {
      value.getClass();
  ensureLowerTraceIsMutable();
      lowerTrace_.set(index, value);
    }
    /**
     * <code>repeated .presage.physiology.Measurement lower_trace = 3;</code>
     */
    private void addLowerTrace(com.presage.physiology.proto.MetricsProto.Measurement value) {
      value.getClass();
  ensureLowerTraceIsMutable();
      lowerTrace_.add(value);
    }
    /**
     * <code>repeated .presage.physiology.Measurement lower_trace = 3;</code>
     */
    private void addLowerTrace(
        int index, com.presage.physiology.proto.MetricsProto.Measurement value) {
      value.getClass();
  ensureLowerTraceIsMutable();
      lowerTrace_.add(index, value);
    }
    /**
     * <code>repeated .presage.physiology.Measurement lower_trace = 3;</code>
     */
    private void addAllLowerTrace(
        java.lang.Iterable<? extends com.presage.physiology.proto.MetricsProto.Measurement> values) {
      ensureLowerTraceIsMutable();
      com.google.protobuf.AbstractMessageLite.addAll(
          values, lowerTrace_);
    }
    /**
     * <code>repeated .presage.physiology.Measurement lower_trace = 3;</code>
     */
    private void clearLowerTrace() {
      lowerTrace_ = emptyProtobufList();
    }
    /**
     * <code>repeated .presage.physiology.Measurement lower_trace = 3;</code>
     */
    private void removeLowerTrace(int index) {
      ensureLowerTraceIsMutable();
      lowerTrace_.remove(index);
    }

    public static final int AMPLITUDE_FIELD_NUMBER = 4;
    private com.google.protobuf.Internal.ProtobufList<com.presage.physiology.proto.MetricsProto.Measurement> amplitude_;
    /**
     * <code>repeated .presage.physiology.Measurement amplitude = 4;</code>
     */
    @java.lang.Override
    public java.util.List<com.presage.physiology.proto.MetricsProto.Measurement> getAmplitudeList() {
      return amplitude_;
    }
    /**
     * <code>repeated .presage.physiology.Measurement amplitude = 4;</code>
     */
    public java.util.List<? extends com.presage.physiology.proto.MetricsProto.MeasurementOrBuilder> 
        getAmplitudeOrBuilderList() {
      return amplitude_;
    }
    /**
     * <code>repeated .presage.physiology.Measurement amplitude = 4;</code>
     */
    @java.lang.Override
    public int getAmplitudeCount() {
      return amplitude_.size();
    }
    /**
     * <code>repeated .presage.physiology.Measurement amplitude = 4;</code>
     */
    @java.lang.Override
    public com.presage.physiology.proto.MetricsProto.Measurement getAmplitude(int index) {
      return amplitude_.get(index);
    }
    /**
     * <code>repeated .presage.physiology.Measurement amplitude = 4;</code>
     */
    public com.presage.physiology.proto.MetricsProto.MeasurementOrBuilder getAmplitudeOrBuilder(
        int index) {
      return amplitude_.get(index);
    }
    private void ensureAmplitudeIsMutable() {
      com.google.protobuf.Internal.ProtobufList<com.presage.physiology.proto.MetricsProto.Measurement> tmp = amplitude_;
      if (!tmp.isModifiable()) {
        amplitude_ =
            com.google.protobuf.GeneratedMessageLite.mutableCopy(tmp);
       }
    }

    /**
     * <code>repeated .presage.physiology.Measurement amplitude = 4;</code>
     */
    private void setAmplitude(
        int index, com.presage.physiology.proto.MetricsProto.Measurement value) {
      value.getClass();
  ensureAmplitudeIsMutable();
      amplitude_.set(index, value);
    }
    /**
     * <code>repeated .presage.physiology.Measurement amplitude = 4;</code>
     */
    private void addAmplitude(com.presage.physiology.proto.MetricsProto.Measurement value) {
      value.getClass();
  ensureAmplitudeIsMutable();
      amplitude_.add(value);
    }
    /**
     * <code>repeated .presage.physiology.Measurement amplitude = 4;</code>
     */
    private void addAmplitude(
        int index, com.presage.physiology.proto.MetricsProto.Measurement value) {
      value.getClass();
  ensureAmplitudeIsMutable();
      amplitude_.add(index, value);
    }
    /**
     * <code>repeated .presage.physiology.Measurement amplitude = 4;</code>
     */
    private void addAllAmplitude(
        java.lang.Iterable<? extends com.presage.physiology.proto.MetricsProto.Measurement> values) {
      ensureAmplitudeIsMutable();
      com.google.protobuf.AbstractMessageLite.addAll(
          values, amplitude_);
    }
    /**
     * <code>repeated .presage.physiology.Measurement amplitude = 4;</code>
     */
    private void clearAmplitude() {
      amplitude_ = emptyProtobufList();
    }
    /**
     * <code>repeated .presage.physiology.Measurement amplitude = 4;</code>
     */
    private void removeAmplitude(int index) {
      ensureAmplitudeIsMutable();
      amplitude_.remove(index);
    }

    public static final int APNEA_FIELD_NUMBER = 5;
    private com.google.protobuf.Internal.ProtobufList<com.presage.physiology.proto.MetricsProto.DetectionStatus> apnea_;
    /**
     * <code>repeated .presage.physiology.DetectionStatus apnea = 5;</code>
     */
    @java.lang.Override
    public java.util.List<com.presage.physiology.proto.MetricsProto.DetectionStatus> getApneaList() {
      return apnea_;
    }
    /**
     * <code>repeated .presage.physiology.DetectionStatus apnea = 5;</code>
     */
    public java.util.List<? extends com.presage.physiology.proto.MetricsProto.DetectionStatusOrBuilder> 
        getApneaOrBuilderList() {
      return apnea_;
    }
    /**
     * <code>repeated .presage.physiology.DetectionStatus apnea = 5;</code>
     */
    @java.lang.Override
    public int getApneaCount() {
      return apnea_.size();
    }
    /**
     * <code>repeated .presage.physiology.DetectionStatus apnea = 5;</code>
     */
    @java.lang.Override
    public com.presage.physiology.proto.MetricsProto.DetectionStatus getApnea(int index) {
      return apnea_.get(index);
    }
    /**
     * <code>repeated .presage.physiology.DetectionStatus apnea = 5;</code>
     */
    public com.presage.physiology.proto.MetricsProto.DetectionStatusOrBuilder getApneaOrBuilder(
        int index) {
      return apnea_.get(index);
    }
    private void ensureApneaIsMutable() {
      com.google.protobuf.Internal.ProtobufList<com.presage.physiology.proto.MetricsProto.DetectionStatus> tmp = apnea_;
      if (!tmp.isModifiable()) {
        apnea_ =
            com.google.protobuf.GeneratedMessageLite.mutableCopy(tmp);
       }
    }

    /**
     * <code>repeated .presage.physiology.DetectionStatus apnea = 5;</code>
     */
    private void setApnea(
        int index, com.presage.physiology.proto.MetricsProto.DetectionStatus value) {
      value.getClass();
  ensureApneaIsMutable();
      apnea_.set(index, value);
    }
    /**
     * <code>repeated .presage.physiology.DetectionStatus apnea = 5;</code>
     */
    private void addApnea(com.presage.physiology.proto.MetricsProto.DetectionStatus value) {
      value.getClass();
  ensureApneaIsMutable();
      apnea_.add(value);
    }
    /**
     * <code>repeated .presage.physiology.DetectionStatus apnea = 5;</code>
     */
    private void addApnea(
        int index, com.presage.physiology.proto.MetricsProto.DetectionStatus value) {
      value.getClass();
  ensureApneaIsMutable();
      apnea_.add(index, value);
    }
    /**
     * <code>repeated .presage.physiology.DetectionStatus apnea = 5;</code>
     */
    private void addAllApnea(
        java.lang.Iterable<? extends com.presage.physiology.proto.MetricsProto.DetectionStatus> values) {
      ensureApneaIsMutable();
      com.google.protobuf.AbstractMessageLite.addAll(
          values, apnea_);
    }
    /**
     * <code>repeated .presage.physiology.DetectionStatus apnea = 5;</code>
     */
    private void clearApnea() {
      apnea_ = emptyProtobufList();
    }
    /**
     * <code>repeated .presage.physiology.DetectionStatus apnea = 5;</code>
     */
    private void removeApnea(int index) {
      ensureApneaIsMutable();
      apnea_.remove(index);
    }

    public static final int RESPIRATORY_LINE_LENGTH_FIELD_NUMBER = 6;
    private com.google.protobuf.Internal.ProtobufList<com.presage.physiology.proto.MetricsProto.Measurement> respiratoryLineLength_;
    /**
     * <code>repeated .presage.physiology.Measurement respiratory_line_length = 6;</code>
     */
    @java.lang.Override
    public java.util.List<com.presage.physiology.proto.MetricsProto.Measurement> getRespiratoryLineLengthList() {
      return respiratoryLineLength_;
    }
    /**
     * <code>repeated .presage.physiology.Measurement respiratory_line_length = 6;</code>
     */
    public java.util.List<? extends com.presage.physiology.proto.MetricsProto.MeasurementOrBuilder> 
        getRespiratoryLineLengthOrBuilderList() {
      return respiratoryLineLength_;
    }
    /**
     * <code>repeated .presage.physiology.Measurement respiratory_line_length = 6;</code>
     */
    @java.lang.Override
    public int getRespiratoryLineLengthCount() {
      return respiratoryLineLength_.size();
    }
    /**
     * <code>repeated .presage.physiology.Measurement respiratory_line_length = 6;</code>
     */
    @java.lang.Override
    public com.presage.physiology.proto.MetricsProto.Measurement getRespiratoryLineLength(int index) {
      return respiratoryLineLength_.get(index);
    }
    /**
     * <code>repeated .presage.physiology.Measurement respiratory_line_length = 6;</code>
     */
    public com.presage.physiology.proto.MetricsProto.MeasurementOrBuilder getRespiratoryLineLengthOrBuilder(
        int index) {
      return respiratoryLineLength_.get(index);
    }
    private void ensureRespiratoryLineLengthIsMutable() {
      com.google.protobuf.Internal.ProtobufList<com.presage.physiology.proto.MetricsProto.Measurement> tmp = respiratoryLineLength_;
      if (!tmp.isModifiable()) {
        respiratoryLineLength_ =
            com.google.protobuf.GeneratedMessageLite.mutableCopy(tmp);
       }
    }

    /**
     * <code>repeated .presage.physiology.Measurement respiratory_line_length = 6;</code>
     */
    private void setRespiratoryLineLength(
        int index, com.presage.physiology.proto.MetricsProto.Measurement value) {
      value.getClass();
  ensureRespiratoryLineLengthIsMutable();
      respiratoryLineLength_.set(index, value);
    }
    /**
     * <code>repeated .presage.physiology.Measurement respiratory_line_length = 6;</code>
     */
    private void addRespiratoryLineLength(com.presage.physiology.proto.MetricsProto.Measurement value) {
      value.getClass();
  ensureRespiratoryLineLengthIsMutable();
      respiratoryLineLength_.add(value);
    }
    /**
     * <code>repeated .presage.physiology.Measurement respiratory_line_length = 6;</code>
     */
    private void addRespiratoryLineLength(
        int index, com.presage.physiology.proto.MetricsProto.Measurement value) {
      value.getClass();
  ensureRespiratoryLineLengthIsMutable();
      respiratoryLineLength_.add(index, value);
    }
    /**
     * <code>repeated .presage.physiology.Measurement respiratory_line_length = 6;</code>
     */
    private void addAllRespiratoryLineLength(
        java.lang.Iterable<? extends com.presage.physiology.proto.MetricsProto.Measurement> values) {
      ensureRespiratoryLineLengthIsMutable();
      com.google.protobuf.AbstractMessageLite.addAll(
          values, respiratoryLineLength_);
    }
    /**
     * <code>repeated .presage.physiology.Measurement respiratory_line_length = 6;</code>
     */
    private void clearRespiratoryLineLength() {
      respiratoryLineLength_ = emptyProtobufList();
    }
    /**
     * <code>repeated .presage.physiology.Measurement respiratory_line_length = 6;</code>
     */
    private void removeRespiratoryLineLength(int index) {
      ensureRespiratoryLineLengthIsMutable();
      respiratoryLineLength_.remove(index);
    }

    public static final int BASELINE_FIELD_NUMBER = 7;
    private com.google.protobuf.Internal.ProtobufList<com.presage.physiology.proto.MetricsProto.Measurement> baseline_;
    /**
     * <code>repeated .presage.physiology.Measurement baseline = 7;</code>
     */
    @java.lang.Override
    public java.util.List<com.presage.physiology.proto.MetricsProto.Measurement> getBaselineList() {
      return baseline_;
    }
    /**
     * <code>repeated .presage.physiology.Measurement baseline = 7;</code>
     */
    public java.util.List<? extends com.presage.physiology.proto.MetricsProto.MeasurementOrBuilder> 
        getBaselineOrBuilderList() {
      return baseline_;
    }
    /**
     * <code>repeated .presage.physiology.Measurement baseline = 7;</code>
     */
    @java.lang.Override
    public int getBaselineCount() {
      return baseline_.size();
    }
    /**
     * <code>repeated .presage.physiology.Measurement baseline = 7;</code>
     */
    @java.lang.Override
    public com.presage.physiology.proto.MetricsProto.Measurement getBaseline(int index) {
      return baseline_.get(index);
    }
    /**
     * <code>repeated .presage.physiology.Measurement baseline = 7;</code>
     */
    public com.presage.physiology.proto.MetricsProto.MeasurementOrBuilder getBaselineOrBuilder(
        int index) {
      return baseline_.get(index);
    }
    private void ensureBaselineIsMutable() {
      com.google.protobuf.Internal.ProtobufList<com.presage.physiology.proto.MetricsProto.Measurement> tmp = baseline_;
      if (!tmp.isModifiable()) {
        baseline_ =
            com.google.protobuf.GeneratedMessageLite.mutableCopy(tmp);
       }
    }

    /**
     * <code>repeated .presage.physiology.Measurement baseline = 7;</code>
     */
    private void setBaseline(
        int index, com.presage.physiology.proto.MetricsProto.Measurement value) {
      value.getClass();
  ensureBaselineIsMutable();
      baseline_.set(index, value);
    }
    /**
     * <code>repeated .presage.physiology.Measurement baseline = 7;</code>
     */
    private void addBaseline(com.presage.physiology.proto.MetricsProto.Measurement value) {
      value.getClass();
  ensureBaselineIsMutable();
      baseline_.add(value);
    }
    /**
     * <code>repeated .presage.physiology.Measurement baseline = 7;</code>
     */
    private void addBaseline(
        int index, com.presage.physiology.proto.MetricsProto.Measurement value) {
      value.getClass();
  ensureBaselineIsMutable();
      baseline_.add(index, value);
    }
    /**
     * <code>repeated .presage.physiology.Measurement baseline = 7;</code>
     */
    private void addAllBaseline(
        java.lang.Iterable<? extends com.presage.physiology.proto.MetricsProto.Measurement> values) {
      ensureBaselineIsMutable();
      com.google.protobuf.AbstractMessageLite.addAll(
          values, baseline_);
    }
    /**
     * <code>repeated .presage.physiology.Measurement baseline = 7;</code>
     */
    private void clearBaseline() {
      baseline_ = emptyProtobufList();
    }
    /**
     * <code>repeated .presage.physiology.Measurement baseline = 7;</code>
     */
    private void removeBaseline(int index) {
      ensureBaselineIsMutable();
      baseline_.remove(index);
    }

    public static final int INHALE_EXHALE_RATIO_FIELD_NUMBER = 8;
    private com.google.protobuf.Internal.ProtobufList<com.presage.physiology.proto.MetricsProto.Measurement> inhaleExhaleRatio_;
    /**
     * <code>repeated .presage.physiology.Measurement inhale_exhale_ratio = 8;</code>
     */
    @java.lang.Override
    public java.util.List<com.presage.physiology.proto.MetricsProto.Measurement> getInhaleExhaleRatioList() {
      return inhaleExhaleRatio_;
    }
    /**
     * <code>repeated .presage.physiology.Measurement inhale_exhale_ratio = 8;</code>
     */
    public java.util.List<? extends com.presage.physiology.proto.MetricsProto.MeasurementOrBuilder> 
        getInhaleExhaleRatioOrBuilderList() {
      return inhaleExhaleRatio_;
    }
    /**
     * <code>repeated .presage.physiology.Measurement inhale_exhale_ratio = 8;</code>
     */
    @java.lang.Override
    public int getInhaleExhaleRatioCount() {
      return inhaleExhaleRatio_.size();
    }
    /**
     * <code>repeated .presage.physiology.Measurement inhale_exhale_ratio = 8;</code>
     */
    @java.lang.Override
    public com.presage.physiology.proto.MetricsProto.Measurement getInhaleExhaleRatio(int index) {
      return inhaleExhaleRatio_.get(index);
    }
    /**
     * <code>repeated .presage.physiology.Measurement inhale_exhale_ratio = 8;</code>
     */
    public com.presage.physiology.proto.MetricsProto.MeasurementOrBuilder getInhaleExhaleRatioOrBuilder(
        int index) {
      return inhaleExhaleRatio_.get(index);
    }
    private void ensureInhaleExhaleRatioIsMutable() {
      com.google.protobuf.Internal.ProtobufList<com.presage.physiology.proto.MetricsProto.Measurement> tmp = inhaleExhaleRatio_;
      if (!tmp.isModifiable()) {
        inhaleExhaleRatio_ =
            com.google.protobuf.GeneratedMessageLite.mutableCopy(tmp);
       }
    }

    /**
     * <code>repeated .presage.physiology.Measurement inhale_exhale_ratio = 8;</code>
     */
    private void setInhaleExhaleRatio(
        int index, com.presage.physiology.proto.MetricsProto.Measurement value) {
      value.getClass();
  ensureInhaleExhaleRatioIsMutable();
      inhaleExhaleRatio_.set(index, value);
    }
    /**
     * <code>repeated .presage.physiology.Measurement inhale_exhale_ratio = 8;</code>
     */
    private void addInhaleExhaleRatio(com.presage.physiology.proto.MetricsProto.Measurement value) {
      value.getClass();
  ensureInhaleExhaleRatioIsMutable();
      inhaleExhaleRatio_.add(value);
    }
    /**
     * <code>repeated .presage.physiology.Measurement inhale_exhale_ratio = 8;</code>
     */
    private void addInhaleExhaleRatio(
        int index, com.presage.physiology.proto.MetricsProto.Measurement value) {
      value.getClass();
  ensureInhaleExhaleRatioIsMutable();
      inhaleExhaleRatio_.add(index, value);
    }
    /**
     * <code>repeated .presage.physiology.Measurement inhale_exhale_ratio = 8;</code>
     */
    private void addAllInhaleExhaleRatio(
        java.lang.Iterable<? extends com.presage.physiology.proto.MetricsProto.Measurement> values) {
      ensureInhaleExhaleRatioIsMutable();
      com.google.protobuf.AbstractMessageLite.addAll(
          values, inhaleExhaleRatio_);
    }
    /**
     * <code>repeated .presage.physiology.Measurement inhale_exhale_ratio = 8;</code>
     */
    private void clearInhaleExhaleRatio() {
      inhaleExhaleRatio_ = emptyProtobufList();
    }
    /**
     * <code>repeated .presage.physiology.Measurement inhale_exhale_ratio = 8;</code>
     */
    private void removeInhaleExhaleRatio(int index) {
      ensureInhaleExhaleRatioIsMutable();
      inhaleExhaleRatio_.remove(index);
    }

    public static final int STRICT_FIELD_NUMBER = 9;
    private com.presage.physiology.proto.MetricsProto.Strict strict_;
    /**
     * <code>.presage.physiology.Strict strict = 9;</code>
     */
    @java.lang.Override
    public boolean hasStrict() {
      return strict_ != null;
    }
    /**
     * <code>.presage.physiology.Strict strict = 9;</code>
     */
    @java.lang.Override
    public com.presage.physiology.proto.MetricsProto.Strict getStrict() {
      return strict_ == null ? com.presage.physiology.proto.MetricsProto.Strict.getDefaultInstance() : strict_;
    }
    /**
     * <code>.presage.physiology.Strict strict = 9;</code>
     */
    private void setStrict(com.presage.physiology.proto.MetricsProto.Strict value) {
      value.getClass();
  strict_ = value;
      
      }
    /**
     * <code>.presage.physiology.Strict strict = 9;</code>
     */
    @java.lang.SuppressWarnings({"ReferenceEquality"})
    private void mergeStrict(com.presage.physiology.proto.MetricsProto.Strict value) {
      value.getClass();
  if (strict_ != null &&
          strict_ != com.presage.physiology.proto.MetricsProto.Strict.getDefaultInstance()) {
        strict_ =
          com.presage.physiology.proto.MetricsProto.Strict.newBuilder(strict_).mergeFrom(value).buildPartial();
      } else {
        strict_ = value;
      }
      
    }
    /**
     * <code>.presage.physiology.Strict strict = 9;</code>
     */
    private void clearStrict() {  strict_ = null;
      
    }

    public static com.presage.physiology.proto.MetricsProto.Breathing parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.MetricsProto.Breathing parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.Breathing parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.MetricsProto.Breathing parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.Breathing parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.MetricsProto.Breathing parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.Breathing parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.MetricsProto.Breathing parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.Breathing parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.MetricsProto.Breathing parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.Breathing parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.MetricsProto.Breathing parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }

    public static Builder newBuilder() {
      return (Builder) DEFAULT_INSTANCE.createBuilder();
    }
    public static Builder newBuilder(com.presage.physiology.proto.MetricsProto.Breathing prototype) {
      return (Builder) DEFAULT_INSTANCE.createBuilder(prototype);
    }

    /**
     * Protobuf type {@code presage.physiology.Breathing}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageLite.Builder<
          com.presage.physiology.proto.MetricsProto.Breathing, Builder> implements
        // @@protoc_insertion_point(builder_implements:presage.physiology.Breathing)
        com.presage.physiology.proto.MetricsProto.BreathingOrBuilder {
      // Construct using com.presage.physiology.proto.MetricsProto.Breathing.newBuilder()
      private Builder() {
        super(DEFAULT_INSTANCE);
      }


      /**
       * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
       */
      @java.lang.Override
      public java.util.List<com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence> getRateList() {
        return java.util.Collections.unmodifiableList(
            instance.getRateList());
      }
      /**
       * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
       */
      @java.lang.Override
      public int getRateCount() {
        return instance.getRateCount();
      }/**
       * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
       */
      @java.lang.Override
      public com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence getRate(int index) {
        return instance.getRate(index);
      }
      /**
       * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
       */
      public Builder setRate(
          int index, com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence value) {
        copyOnWrite();
        instance.setRate(index, value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
       */
      public Builder setRate(
          int index, com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence.Builder builderForValue) {
        copyOnWrite();
        instance.setRate(index,
            builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
       */
      public Builder addRate(com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence value) {
        copyOnWrite();
        instance.addRate(value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
       */
      public Builder addRate(
          int index, com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence value) {
        copyOnWrite();
        instance.addRate(index, value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
       */
      public Builder addRate(
          com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence.Builder builderForValue) {
        copyOnWrite();
        instance.addRate(builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
       */
      public Builder addRate(
          int index, com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence.Builder builderForValue) {
        copyOnWrite();
        instance.addRate(index,
            builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
       */
      public Builder addAllRate(
          java.lang.Iterable<? extends com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence> values) {
        copyOnWrite();
        instance.addAllRate(values);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
       */
      public Builder clearRate() {
        copyOnWrite();
        instance.clearRate();
        return this;
      }
      /**
       * <code>repeated .presage.physiology.MeasurementWithConfidence rate = 1;</code>
       */
      public Builder removeRate(int index) {
        copyOnWrite();
        instance.removeRate(index);
        return this;
      }

      /**
       * <code>repeated .presage.physiology.Measurement upper_trace = 2;</code>
       */
      @java.lang.Override
      public java.util.List<com.presage.physiology.proto.MetricsProto.Measurement> getUpperTraceList() {
        return java.util.Collections.unmodifiableList(
            instance.getUpperTraceList());
      }
      /**
       * <code>repeated .presage.physiology.Measurement upper_trace = 2;</code>
       */
      @java.lang.Override
      public int getUpperTraceCount() {
        return instance.getUpperTraceCount();
      }/**
       * <code>repeated .presage.physiology.Measurement upper_trace = 2;</code>
       */
      @java.lang.Override
      public com.presage.physiology.proto.MetricsProto.Measurement getUpperTrace(int index) {
        return instance.getUpperTrace(index);
      }
      /**
       * <code>repeated .presage.physiology.Measurement upper_trace = 2;</code>
       */
      public Builder setUpperTrace(
          int index, com.presage.physiology.proto.MetricsProto.Measurement value) {
        copyOnWrite();
        instance.setUpperTrace(index, value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement upper_trace = 2;</code>
       */
      public Builder setUpperTrace(
          int index, com.presage.physiology.proto.MetricsProto.Measurement.Builder builderForValue) {
        copyOnWrite();
        instance.setUpperTrace(index,
            builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement upper_trace = 2;</code>
       */
      public Builder addUpperTrace(com.presage.physiology.proto.MetricsProto.Measurement value) {
        copyOnWrite();
        instance.addUpperTrace(value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement upper_trace = 2;</code>
       */
      public Builder addUpperTrace(
          int index, com.presage.physiology.proto.MetricsProto.Measurement value) {
        copyOnWrite();
        instance.addUpperTrace(index, value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement upper_trace = 2;</code>
       */
      public Builder addUpperTrace(
          com.presage.physiology.proto.MetricsProto.Measurement.Builder builderForValue) {
        copyOnWrite();
        instance.addUpperTrace(builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement upper_trace = 2;</code>
       */
      public Builder addUpperTrace(
          int index, com.presage.physiology.proto.MetricsProto.Measurement.Builder builderForValue) {
        copyOnWrite();
        instance.addUpperTrace(index,
            builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement upper_trace = 2;</code>
       */
      public Builder addAllUpperTrace(
          java.lang.Iterable<? extends com.presage.physiology.proto.MetricsProto.Measurement> values) {
        copyOnWrite();
        instance.addAllUpperTrace(values);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement upper_trace = 2;</code>
       */
      public Builder clearUpperTrace() {
        copyOnWrite();
        instance.clearUpperTrace();
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement upper_trace = 2;</code>
       */
      public Builder removeUpperTrace(int index) {
        copyOnWrite();
        instance.removeUpperTrace(index);
        return this;
      }

      /**
       * <code>repeated .presage.physiology.Measurement lower_trace = 3;</code>
       */
      @java.lang.Override
      public java.util.List<com.presage.physiology.proto.MetricsProto.Measurement> getLowerTraceList() {
        return java.util.Collections.unmodifiableList(
            instance.getLowerTraceList());
      }
      /**
       * <code>repeated .presage.physiology.Measurement lower_trace = 3;</code>
       */
      @java.lang.Override
      public int getLowerTraceCount() {
        return instance.getLowerTraceCount();
      }/**
       * <code>repeated .presage.physiology.Measurement lower_trace = 3;</code>
       */
      @java.lang.Override
      public com.presage.physiology.proto.MetricsProto.Measurement getLowerTrace(int index) {
        return instance.getLowerTrace(index);
      }
      /**
       * <code>repeated .presage.physiology.Measurement lower_trace = 3;</code>
       */
      public Builder setLowerTrace(
          int index, com.presage.physiology.proto.MetricsProto.Measurement value) {
        copyOnWrite();
        instance.setLowerTrace(index, value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement lower_trace = 3;</code>
       */
      public Builder setLowerTrace(
          int index, com.presage.physiology.proto.MetricsProto.Measurement.Builder builderForValue) {
        copyOnWrite();
        instance.setLowerTrace(index,
            builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement lower_trace = 3;</code>
       */
      public Builder addLowerTrace(com.presage.physiology.proto.MetricsProto.Measurement value) {
        copyOnWrite();
        instance.addLowerTrace(value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement lower_trace = 3;</code>
       */
      public Builder addLowerTrace(
          int index, com.presage.physiology.proto.MetricsProto.Measurement value) {
        copyOnWrite();
        instance.addLowerTrace(index, value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement lower_trace = 3;</code>
       */
      public Builder addLowerTrace(
          com.presage.physiology.proto.MetricsProto.Measurement.Builder builderForValue) {
        copyOnWrite();
        instance.addLowerTrace(builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement lower_trace = 3;</code>
       */
      public Builder addLowerTrace(
          int index, com.presage.physiology.proto.MetricsProto.Measurement.Builder builderForValue) {
        copyOnWrite();
        instance.addLowerTrace(index,
            builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement lower_trace = 3;</code>
       */
      public Builder addAllLowerTrace(
          java.lang.Iterable<? extends com.presage.physiology.proto.MetricsProto.Measurement> values) {
        copyOnWrite();
        instance.addAllLowerTrace(values);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement lower_trace = 3;</code>
       */
      public Builder clearLowerTrace() {
        copyOnWrite();
        instance.clearLowerTrace();
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement lower_trace = 3;</code>
       */
      public Builder removeLowerTrace(int index) {
        copyOnWrite();
        instance.removeLowerTrace(index);
        return this;
      }

      /**
       * <code>repeated .presage.physiology.Measurement amplitude = 4;</code>
       */
      @java.lang.Override
      public java.util.List<com.presage.physiology.proto.MetricsProto.Measurement> getAmplitudeList() {
        return java.util.Collections.unmodifiableList(
            instance.getAmplitudeList());
      }
      /**
       * <code>repeated .presage.physiology.Measurement amplitude = 4;</code>
       */
      @java.lang.Override
      public int getAmplitudeCount() {
        return instance.getAmplitudeCount();
      }/**
       * <code>repeated .presage.physiology.Measurement amplitude = 4;</code>
       */
      @java.lang.Override
      public com.presage.physiology.proto.MetricsProto.Measurement getAmplitude(int index) {
        return instance.getAmplitude(index);
      }
      /**
       * <code>repeated .presage.physiology.Measurement amplitude = 4;</code>
       */
      public Builder setAmplitude(
          int index, com.presage.physiology.proto.MetricsProto.Measurement value) {
        copyOnWrite();
        instance.setAmplitude(index, value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement amplitude = 4;</code>
       */
      public Builder setAmplitude(
          int index, com.presage.physiology.proto.MetricsProto.Measurement.Builder builderForValue) {
        copyOnWrite();
        instance.setAmplitude(index,
            builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement amplitude = 4;</code>
       */
      public Builder addAmplitude(com.presage.physiology.proto.MetricsProto.Measurement value) {
        copyOnWrite();
        instance.addAmplitude(value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement amplitude = 4;</code>
       */
      public Builder addAmplitude(
          int index, com.presage.physiology.proto.MetricsProto.Measurement value) {
        copyOnWrite();
        instance.addAmplitude(index, value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement amplitude = 4;</code>
       */
      public Builder addAmplitude(
          com.presage.physiology.proto.MetricsProto.Measurement.Builder builderForValue) {
        copyOnWrite();
        instance.addAmplitude(builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement amplitude = 4;</code>
       */
      public Builder addAmplitude(
          int index, com.presage.physiology.proto.MetricsProto.Measurement.Builder builderForValue) {
        copyOnWrite();
        instance.addAmplitude(index,
            builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement amplitude = 4;</code>
       */
      public Builder addAllAmplitude(
          java.lang.Iterable<? extends com.presage.physiology.proto.MetricsProto.Measurement> values) {
        copyOnWrite();
        instance.addAllAmplitude(values);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement amplitude = 4;</code>
       */
      public Builder clearAmplitude() {
        copyOnWrite();
        instance.clearAmplitude();
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement amplitude = 4;</code>
       */
      public Builder removeAmplitude(int index) {
        copyOnWrite();
        instance.removeAmplitude(index);
        return this;
      }

      /**
       * <code>repeated .presage.physiology.DetectionStatus apnea = 5;</code>
       */
      @java.lang.Override
      public java.util.List<com.presage.physiology.proto.MetricsProto.DetectionStatus> getApneaList() {
        return java.util.Collections.unmodifiableList(
            instance.getApneaList());
      }
      /**
       * <code>repeated .presage.physiology.DetectionStatus apnea = 5;</code>
       */
      @java.lang.Override
      public int getApneaCount() {
        return instance.getApneaCount();
      }/**
       * <code>repeated .presage.physiology.DetectionStatus apnea = 5;</code>
       */
      @java.lang.Override
      public com.presage.physiology.proto.MetricsProto.DetectionStatus getApnea(int index) {
        return instance.getApnea(index);
      }
      /**
       * <code>repeated .presage.physiology.DetectionStatus apnea = 5;</code>
       */
      public Builder setApnea(
          int index, com.presage.physiology.proto.MetricsProto.DetectionStatus value) {
        copyOnWrite();
        instance.setApnea(index, value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.DetectionStatus apnea = 5;</code>
       */
      public Builder setApnea(
          int index, com.presage.physiology.proto.MetricsProto.DetectionStatus.Builder builderForValue) {
        copyOnWrite();
        instance.setApnea(index,
            builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.DetectionStatus apnea = 5;</code>
       */
      public Builder addApnea(com.presage.physiology.proto.MetricsProto.DetectionStatus value) {
        copyOnWrite();
        instance.addApnea(value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.DetectionStatus apnea = 5;</code>
       */
      public Builder addApnea(
          int index, com.presage.physiology.proto.MetricsProto.DetectionStatus value) {
        copyOnWrite();
        instance.addApnea(index, value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.DetectionStatus apnea = 5;</code>
       */
      public Builder addApnea(
          com.presage.physiology.proto.MetricsProto.DetectionStatus.Builder builderForValue) {
        copyOnWrite();
        instance.addApnea(builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.DetectionStatus apnea = 5;</code>
       */
      public Builder addApnea(
          int index, com.presage.physiology.proto.MetricsProto.DetectionStatus.Builder builderForValue) {
        copyOnWrite();
        instance.addApnea(index,
            builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.DetectionStatus apnea = 5;</code>
       */
      public Builder addAllApnea(
          java.lang.Iterable<? extends com.presage.physiology.proto.MetricsProto.DetectionStatus> values) {
        copyOnWrite();
        instance.addAllApnea(values);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.DetectionStatus apnea = 5;</code>
       */
      public Builder clearApnea() {
        copyOnWrite();
        instance.clearApnea();
        return this;
      }
      /**
       * <code>repeated .presage.physiology.DetectionStatus apnea = 5;</code>
       */
      public Builder removeApnea(int index) {
        copyOnWrite();
        instance.removeApnea(index);
        return this;
      }

      /**
       * <code>repeated .presage.physiology.Measurement respiratory_line_length = 6;</code>
       */
      @java.lang.Override
      public java.util.List<com.presage.physiology.proto.MetricsProto.Measurement> getRespiratoryLineLengthList() {
        return java.util.Collections.unmodifiableList(
            instance.getRespiratoryLineLengthList());
      }
      /**
       * <code>repeated .presage.physiology.Measurement respiratory_line_length = 6;</code>
       */
      @java.lang.Override
      public int getRespiratoryLineLengthCount() {
        return instance.getRespiratoryLineLengthCount();
      }/**
       * <code>repeated .presage.physiology.Measurement respiratory_line_length = 6;</code>
       */
      @java.lang.Override
      public com.presage.physiology.proto.MetricsProto.Measurement getRespiratoryLineLength(int index) {
        return instance.getRespiratoryLineLength(index);
      }
      /**
       * <code>repeated .presage.physiology.Measurement respiratory_line_length = 6;</code>
       */
      public Builder setRespiratoryLineLength(
          int index, com.presage.physiology.proto.MetricsProto.Measurement value) {
        copyOnWrite();
        instance.setRespiratoryLineLength(index, value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement respiratory_line_length = 6;</code>
       */
      public Builder setRespiratoryLineLength(
          int index, com.presage.physiology.proto.MetricsProto.Measurement.Builder builderForValue) {
        copyOnWrite();
        instance.setRespiratoryLineLength(index,
            builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement respiratory_line_length = 6;</code>
       */
      public Builder addRespiratoryLineLength(com.presage.physiology.proto.MetricsProto.Measurement value) {
        copyOnWrite();
        instance.addRespiratoryLineLength(value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement respiratory_line_length = 6;</code>
       */
      public Builder addRespiratoryLineLength(
          int index, com.presage.physiology.proto.MetricsProto.Measurement value) {
        copyOnWrite();
        instance.addRespiratoryLineLength(index, value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement respiratory_line_length = 6;</code>
       */
      public Builder addRespiratoryLineLength(
          com.presage.physiology.proto.MetricsProto.Measurement.Builder builderForValue) {
        copyOnWrite();
        instance.addRespiratoryLineLength(builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement respiratory_line_length = 6;</code>
       */
      public Builder addRespiratoryLineLength(
          int index, com.presage.physiology.proto.MetricsProto.Measurement.Builder builderForValue) {
        copyOnWrite();
        instance.addRespiratoryLineLength(index,
            builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement respiratory_line_length = 6;</code>
       */
      public Builder addAllRespiratoryLineLength(
          java.lang.Iterable<? extends com.presage.physiology.proto.MetricsProto.Measurement> values) {
        copyOnWrite();
        instance.addAllRespiratoryLineLength(values);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement respiratory_line_length = 6;</code>
       */
      public Builder clearRespiratoryLineLength() {
        copyOnWrite();
        instance.clearRespiratoryLineLength();
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement respiratory_line_length = 6;</code>
       */
      public Builder removeRespiratoryLineLength(int index) {
        copyOnWrite();
        instance.removeRespiratoryLineLength(index);
        return this;
      }

      /**
       * <code>repeated .presage.physiology.Measurement baseline = 7;</code>
       */
      @java.lang.Override
      public java.util.List<com.presage.physiology.proto.MetricsProto.Measurement> getBaselineList() {
        return java.util.Collections.unmodifiableList(
            instance.getBaselineList());
      }
      /**
       * <code>repeated .presage.physiology.Measurement baseline = 7;</code>
       */
      @java.lang.Override
      public int getBaselineCount() {
        return instance.getBaselineCount();
      }/**
       * <code>repeated .presage.physiology.Measurement baseline = 7;</code>
       */
      @java.lang.Override
      public com.presage.physiology.proto.MetricsProto.Measurement getBaseline(int index) {
        return instance.getBaseline(index);
      }
      /**
       * <code>repeated .presage.physiology.Measurement baseline = 7;</code>
       */
      public Builder setBaseline(
          int index, com.presage.physiology.proto.MetricsProto.Measurement value) {
        copyOnWrite();
        instance.setBaseline(index, value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement baseline = 7;</code>
       */
      public Builder setBaseline(
          int index, com.presage.physiology.proto.MetricsProto.Measurement.Builder builderForValue) {
        copyOnWrite();
        instance.setBaseline(index,
            builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement baseline = 7;</code>
       */
      public Builder addBaseline(com.presage.physiology.proto.MetricsProto.Measurement value) {
        copyOnWrite();
        instance.addBaseline(value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement baseline = 7;</code>
       */
      public Builder addBaseline(
          int index, com.presage.physiology.proto.MetricsProto.Measurement value) {
        copyOnWrite();
        instance.addBaseline(index, value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement baseline = 7;</code>
       */
      public Builder addBaseline(
          com.presage.physiology.proto.MetricsProto.Measurement.Builder builderForValue) {
        copyOnWrite();
        instance.addBaseline(builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement baseline = 7;</code>
       */
      public Builder addBaseline(
          int index, com.presage.physiology.proto.MetricsProto.Measurement.Builder builderForValue) {
        copyOnWrite();
        instance.addBaseline(index,
            builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement baseline = 7;</code>
       */
      public Builder addAllBaseline(
          java.lang.Iterable<? extends com.presage.physiology.proto.MetricsProto.Measurement> values) {
        copyOnWrite();
        instance.addAllBaseline(values);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement baseline = 7;</code>
       */
      public Builder clearBaseline() {
        copyOnWrite();
        instance.clearBaseline();
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement baseline = 7;</code>
       */
      public Builder removeBaseline(int index) {
        copyOnWrite();
        instance.removeBaseline(index);
        return this;
      }

      /**
       * <code>repeated .presage.physiology.Measurement inhale_exhale_ratio = 8;</code>
       */
      @java.lang.Override
      public java.util.List<com.presage.physiology.proto.MetricsProto.Measurement> getInhaleExhaleRatioList() {
        return java.util.Collections.unmodifiableList(
            instance.getInhaleExhaleRatioList());
      }
      /**
       * <code>repeated .presage.physiology.Measurement inhale_exhale_ratio = 8;</code>
       */
      @java.lang.Override
      public int getInhaleExhaleRatioCount() {
        return instance.getInhaleExhaleRatioCount();
      }/**
       * <code>repeated .presage.physiology.Measurement inhale_exhale_ratio = 8;</code>
       */
      @java.lang.Override
      public com.presage.physiology.proto.MetricsProto.Measurement getInhaleExhaleRatio(int index) {
        return instance.getInhaleExhaleRatio(index);
      }
      /**
       * <code>repeated .presage.physiology.Measurement inhale_exhale_ratio = 8;</code>
       */
      public Builder setInhaleExhaleRatio(
          int index, com.presage.physiology.proto.MetricsProto.Measurement value) {
        copyOnWrite();
        instance.setInhaleExhaleRatio(index, value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement inhale_exhale_ratio = 8;</code>
       */
      public Builder setInhaleExhaleRatio(
          int index, com.presage.physiology.proto.MetricsProto.Measurement.Builder builderForValue) {
        copyOnWrite();
        instance.setInhaleExhaleRatio(index,
            builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement inhale_exhale_ratio = 8;</code>
       */
      public Builder addInhaleExhaleRatio(com.presage.physiology.proto.MetricsProto.Measurement value) {
        copyOnWrite();
        instance.addInhaleExhaleRatio(value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement inhale_exhale_ratio = 8;</code>
       */
      public Builder addInhaleExhaleRatio(
          int index, com.presage.physiology.proto.MetricsProto.Measurement value) {
        copyOnWrite();
        instance.addInhaleExhaleRatio(index, value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement inhale_exhale_ratio = 8;</code>
       */
      public Builder addInhaleExhaleRatio(
          com.presage.physiology.proto.MetricsProto.Measurement.Builder builderForValue) {
        copyOnWrite();
        instance.addInhaleExhaleRatio(builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement inhale_exhale_ratio = 8;</code>
       */
      public Builder addInhaleExhaleRatio(
          int index, com.presage.physiology.proto.MetricsProto.Measurement.Builder builderForValue) {
        copyOnWrite();
        instance.addInhaleExhaleRatio(index,
            builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement inhale_exhale_ratio = 8;</code>
       */
      public Builder addAllInhaleExhaleRatio(
          java.lang.Iterable<? extends com.presage.physiology.proto.MetricsProto.Measurement> values) {
        copyOnWrite();
        instance.addAllInhaleExhaleRatio(values);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement inhale_exhale_ratio = 8;</code>
       */
      public Builder clearInhaleExhaleRatio() {
        copyOnWrite();
        instance.clearInhaleExhaleRatio();
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Measurement inhale_exhale_ratio = 8;</code>
       */
      public Builder removeInhaleExhaleRatio(int index) {
        copyOnWrite();
        instance.removeInhaleExhaleRatio(index);
        return this;
      }

      /**
       * <code>.presage.physiology.Strict strict = 9;</code>
       */
      @java.lang.Override
      public boolean hasStrict() {
        return instance.hasStrict();
      }
      /**
       * <code>.presage.physiology.Strict strict = 9;</code>
       */
      @java.lang.Override
      public com.presage.physiology.proto.MetricsProto.Strict getStrict() {
        return instance.getStrict();
      }
      /**
       * <code>.presage.physiology.Strict strict = 9;</code>
       */
      public Builder setStrict(com.presage.physiology.proto.MetricsProto.Strict value) {
        copyOnWrite();
        instance.setStrict(value);
        return this;
        }
      /**
       * <code>.presage.physiology.Strict strict = 9;</code>
       */
      public Builder setStrict(
          com.presage.physiology.proto.MetricsProto.Strict.Builder builderForValue) {
        copyOnWrite();
        instance.setStrict(builderForValue.build());
        return this;
      }
      /**
       * <code>.presage.physiology.Strict strict = 9;</code>
       */
      public Builder mergeStrict(com.presage.physiology.proto.MetricsProto.Strict value) {
        copyOnWrite();
        instance.mergeStrict(value);
        return this;
      }
      /**
       * <code>.presage.physiology.Strict strict = 9;</code>
       */
      public Builder clearStrict() {  copyOnWrite();
        instance.clearStrict();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:presage.physiology.Breathing)
    }
    @java.lang.Override
    @java.lang.SuppressWarnings({"unchecked", "fallthrough"})
    protected final java.lang.Object dynamicMethod(
        com.google.protobuf.GeneratedMessageLite.MethodToInvoke method,
        java.lang.Object arg0, java.lang.Object arg1) {
      switch (method) {
        case NEW_MUTABLE_INSTANCE: {
          return new com.presage.physiology.proto.MetricsProto.Breathing();
        }
        case NEW_BUILDER: {
          return new Builder();
        }
        case BUILD_MESSAGE_INFO: {
            java.lang.Object[] objects = new java.lang.Object[] {
              "rate_",
              com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence.class,
              "upperTrace_",
              com.presage.physiology.proto.MetricsProto.Measurement.class,
              "lowerTrace_",
              com.presage.physiology.proto.MetricsProto.Measurement.class,
              "amplitude_",
              com.presage.physiology.proto.MetricsProto.Measurement.class,
              "apnea_",
              com.presage.physiology.proto.MetricsProto.DetectionStatus.class,
              "respiratoryLineLength_",
              com.presage.physiology.proto.MetricsProto.Measurement.class,
              "baseline_",
              com.presage.physiology.proto.MetricsProto.Measurement.class,
              "inhaleExhaleRatio_",
              com.presage.physiology.proto.MetricsProto.Measurement.class,
              "strict_",
            };
            java.lang.String info =
                "\u0000\t\u0000\u0000\u0001\t\t\u0000\b\u0000\u0001\u001b\u0002\u001b\u0003\u001b" +
                "\u0004\u001b\u0005\u001b\u0006\u001b\u0007\u001b\b\u001b\t\t";
            return newMessageInfo(DEFAULT_INSTANCE, info, objects);
        }
        // fall through
        case GET_DEFAULT_INSTANCE: {
          return DEFAULT_INSTANCE;
        }
        case GET_PARSER: {
          com.google.protobuf.Parser<com.presage.physiology.proto.MetricsProto.Breathing> parser = PARSER;
          if (parser == null) {
            synchronized (com.presage.physiology.proto.MetricsProto.Breathing.class) {
              parser = PARSER;
              if (parser == null) {
                parser =
                    new DefaultInstanceBasedParser<com.presage.physiology.proto.MetricsProto.Breathing>(
                        DEFAULT_INSTANCE);
                PARSER = parser;
              }
            }
          }
          return parser;
      }
      case GET_MEMOIZED_IS_INITIALIZED: {
        return (byte) 1;
      }
      case SET_MEMOIZED_IS_INITIALIZED: {
        return null;
      }
      }
      throw new UnsupportedOperationException();
    }


    // @@protoc_insertion_point(class_scope:presage.physiology.Breathing)
    private static final com.presage.physiology.proto.MetricsProto.Breathing DEFAULT_INSTANCE;
    static {
      Breathing defaultInstance = new Breathing();
      // New instances are implicitly immutable so no need to make
      // immutable.
      DEFAULT_INSTANCE = defaultInstance;
      com.google.protobuf.GeneratedMessageLite.registerDefaultInstance(
        Breathing.class, defaultInstance);
    }

    public static com.presage.physiology.proto.MetricsProto.Breathing getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static volatile com.google.protobuf.Parser<Breathing> PARSER;

    public static com.google.protobuf.Parser<Breathing> parser() {
      return DEFAULT_INSTANCE.getParserForType();
    }
  }

  public interface BloodPressureOrBuilder extends
      // @@protoc_insertion_point(interface_extends:presage.physiology.BloodPressure)
      com.google.protobuf.MessageLiteOrBuilder {

    /**
     * <code>repeated .presage.physiology.MeasurementWithConfidence phasic = 1;</code>
     */
    java.util.List<com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence> 
        getPhasicList();
    /**
     * <code>repeated .presage.physiology.MeasurementWithConfidence phasic = 1;</code>
     */
    com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence getPhasic(int index);
    /**
     * <code>repeated .presage.physiology.MeasurementWithConfidence phasic = 1;</code>
     */
    int getPhasicCount();
  }
  /**
   * Protobuf type {@code presage.physiology.BloodPressure}
   */
  public  static final class BloodPressure extends
      com.google.protobuf.GeneratedMessageLite<
          BloodPressure, BloodPressure.Builder> implements
      // @@protoc_insertion_point(message_implements:presage.physiology.BloodPressure)
      BloodPressureOrBuilder {
    private BloodPressure() {
      phasic_ = emptyProtobufList();
    }
    public static final int PHASIC_FIELD_NUMBER = 1;
    private com.google.protobuf.Internal.ProtobufList<com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence> phasic_;
    /**
     * <code>repeated .presage.physiology.MeasurementWithConfidence phasic = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence> getPhasicList() {
      return phasic_;
    }
    /**
     * <code>repeated .presage.physiology.MeasurementWithConfidence phasic = 1;</code>
     */
    public java.util.List<? extends com.presage.physiology.proto.MetricsProto.MeasurementWithConfidenceOrBuilder> 
        getPhasicOrBuilderList() {
      return phasic_;
    }
    /**
     * <code>repeated .presage.physiology.MeasurementWithConfidence phasic = 1;</code>
     */
    @java.lang.Override
    public int getPhasicCount() {
      return phasic_.size();
    }
    /**
     * <code>repeated .presage.physiology.MeasurementWithConfidence phasic = 1;</code>
     */
    @java.lang.Override
    public com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence getPhasic(int index) {
      return phasic_.get(index);
    }
    /**
     * <code>repeated .presage.physiology.MeasurementWithConfidence phasic = 1;</code>
     */
    public com.presage.physiology.proto.MetricsProto.MeasurementWithConfidenceOrBuilder getPhasicOrBuilder(
        int index) {
      return phasic_.get(index);
    }
    private void ensurePhasicIsMutable() {
      com.google.protobuf.Internal.ProtobufList<com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence> tmp = phasic_;
      if (!tmp.isModifiable()) {
        phasic_ =
            com.google.protobuf.GeneratedMessageLite.mutableCopy(tmp);
       }
    }

    /**
     * <code>repeated .presage.physiology.MeasurementWithConfidence phasic = 1;</code>
     */
    private void setPhasic(
        int index, com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence value) {
      value.getClass();
  ensurePhasicIsMutable();
      phasic_.set(index, value);
    }
    /**
     * <code>repeated .presage.physiology.MeasurementWithConfidence phasic = 1;</code>
     */
    private void addPhasic(com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence value) {
      value.getClass();
  ensurePhasicIsMutable();
      phasic_.add(value);
    }
    /**
     * <code>repeated .presage.physiology.MeasurementWithConfidence phasic = 1;</code>
     */
    private void addPhasic(
        int index, com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence value) {
      value.getClass();
  ensurePhasicIsMutable();
      phasic_.add(index, value);
    }
    /**
     * <code>repeated .presage.physiology.MeasurementWithConfidence phasic = 1;</code>
     */
    private void addAllPhasic(
        java.lang.Iterable<? extends com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence> values) {
      ensurePhasicIsMutable();
      com.google.protobuf.AbstractMessageLite.addAll(
          values, phasic_);
    }
    /**
     * <code>repeated .presage.physiology.MeasurementWithConfidence phasic = 1;</code>
     */
    private void clearPhasic() {
      phasic_ = emptyProtobufList();
    }
    /**
     * <code>repeated .presage.physiology.MeasurementWithConfidence phasic = 1;</code>
     */
    private void removePhasic(int index) {
      ensurePhasicIsMutable();
      phasic_.remove(index);
    }

    public static com.presage.physiology.proto.MetricsProto.BloodPressure parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.MetricsProto.BloodPressure parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.BloodPressure parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.MetricsProto.BloodPressure parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.BloodPressure parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.MetricsProto.BloodPressure parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.BloodPressure parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.MetricsProto.BloodPressure parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.BloodPressure parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.MetricsProto.BloodPressure parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.BloodPressure parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.MetricsProto.BloodPressure parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }

    public static Builder newBuilder() {
      return (Builder) DEFAULT_INSTANCE.createBuilder();
    }
    public static Builder newBuilder(com.presage.physiology.proto.MetricsProto.BloodPressure prototype) {
      return (Builder) DEFAULT_INSTANCE.createBuilder(prototype);
    }

    /**
     * Protobuf type {@code presage.physiology.BloodPressure}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageLite.Builder<
          com.presage.physiology.proto.MetricsProto.BloodPressure, Builder> implements
        // @@protoc_insertion_point(builder_implements:presage.physiology.BloodPressure)
        com.presage.physiology.proto.MetricsProto.BloodPressureOrBuilder {
      // Construct using com.presage.physiology.proto.MetricsProto.BloodPressure.newBuilder()
      private Builder() {
        super(DEFAULT_INSTANCE);
      }


      /**
       * <code>repeated .presage.physiology.MeasurementWithConfidence phasic = 1;</code>
       */
      @java.lang.Override
      public java.util.List<com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence> getPhasicList() {
        return java.util.Collections.unmodifiableList(
            instance.getPhasicList());
      }
      /**
       * <code>repeated .presage.physiology.MeasurementWithConfidence phasic = 1;</code>
       */
      @java.lang.Override
      public int getPhasicCount() {
        return instance.getPhasicCount();
      }/**
       * <code>repeated .presage.physiology.MeasurementWithConfidence phasic = 1;</code>
       */
      @java.lang.Override
      public com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence getPhasic(int index) {
        return instance.getPhasic(index);
      }
      /**
       * <code>repeated .presage.physiology.MeasurementWithConfidence phasic = 1;</code>
       */
      public Builder setPhasic(
          int index, com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence value) {
        copyOnWrite();
        instance.setPhasic(index, value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.MeasurementWithConfidence phasic = 1;</code>
       */
      public Builder setPhasic(
          int index, com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence.Builder builderForValue) {
        copyOnWrite();
        instance.setPhasic(index,
            builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.MeasurementWithConfidence phasic = 1;</code>
       */
      public Builder addPhasic(com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence value) {
        copyOnWrite();
        instance.addPhasic(value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.MeasurementWithConfidence phasic = 1;</code>
       */
      public Builder addPhasic(
          int index, com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence value) {
        copyOnWrite();
        instance.addPhasic(index, value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.MeasurementWithConfidence phasic = 1;</code>
       */
      public Builder addPhasic(
          com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence.Builder builderForValue) {
        copyOnWrite();
        instance.addPhasic(builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.MeasurementWithConfidence phasic = 1;</code>
       */
      public Builder addPhasic(
          int index, com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence.Builder builderForValue) {
        copyOnWrite();
        instance.addPhasic(index,
            builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.MeasurementWithConfidence phasic = 1;</code>
       */
      public Builder addAllPhasic(
          java.lang.Iterable<? extends com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence> values) {
        copyOnWrite();
        instance.addAllPhasic(values);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.MeasurementWithConfidence phasic = 1;</code>
       */
      public Builder clearPhasic() {
        copyOnWrite();
        instance.clearPhasic();
        return this;
      }
      /**
       * <code>repeated .presage.physiology.MeasurementWithConfidence phasic = 1;</code>
       */
      public Builder removePhasic(int index) {
        copyOnWrite();
        instance.removePhasic(index);
        return this;
      }

      // @@protoc_insertion_point(builder_scope:presage.physiology.BloodPressure)
    }
    @java.lang.Override
    @java.lang.SuppressWarnings({"unchecked", "fallthrough"})
    protected final java.lang.Object dynamicMethod(
        com.google.protobuf.GeneratedMessageLite.MethodToInvoke method,
        java.lang.Object arg0, java.lang.Object arg1) {
      switch (method) {
        case NEW_MUTABLE_INSTANCE: {
          return new com.presage.physiology.proto.MetricsProto.BloodPressure();
        }
        case NEW_BUILDER: {
          return new Builder();
        }
        case BUILD_MESSAGE_INFO: {
            java.lang.Object[] objects = new java.lang.Object[] {
              "phasic_",
              com.presage.physiology.proto.MetricsProto.MeasurementWithConfidence.class,
            };
            java.lang.String info =
                "\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u001b";
            return newMessageInfo(DEFAULT_INSTANCE, info, objects);
        }
        // fall through
        case GET_DEFAULT_INSTANCE: {
          return DEFAULT_INSTANCE;
        }
        case GET_PARSER: {
          com.google.protobuf.Parser<com.presage.physiology.proto.MetricsProto.BloodPressure> parser = PARSER;
          if (parser == null) {
            synchronized (com.presage.physiology.proto.MetricsProto.BloodPressure.class) {
              parser = PARSER;
              if (parser == null) {
                parser =
                    new DefaultInstanceBasedParser<com.presage.physiology.proto.MetricsProto.BloodPressure>(
                        DEFAULT_INSTANCE);
                PARSER = parser;
              }
            }
          }
          return parser;
      }
      case GET_MEMOIZED_IS_INITIALIZED: {
        return (byte) 1;
      }
      case SET_MEMOIZED_IS_INITIALIZED: {
        return null;
      }
      }
      throw new UnsupportedOperationException();
    }


    // @@protoc_insertion_point(class_scope:presage.physiology.BloodPressure)
    private static final com.presage.physiology.proto.MetricsProto.BloodPressure DEFAULT_INSTANCE;
    static {
      BloodPressure defaultInstance = new BloodPressure();
      // New instances are implicitly immutable so no need to make
      // immutable.
      DEFAULT_INSTANCE = defaultInstance;
      com.google.protobuf.GeneratedMessageLite.registerDefaultInstance(
        BloodPressure.class, defaultInstance);
    }

    public static com.presage.physiology.proto.MetricsProto.BloodPressure getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static volatile com.google.protobuf.Parser<BloodPressure> PARSER;

    public static com.google.protobuf.Parser<BloodPressure> parser() {
      return DEFAULT_INSTANCE.getParserForType();
    }
  }

  public interface LandmarksOrBuilder extends
      // @@protoc_insertion_point(interface_extends:presage.physiology.Landmarks)
      com.google.protobuf.MessageLiteOrBuilder {

    /**
     * <code>float time = 1;</code>
     * @return The time.
     */
    float getTime();

    /**
     * <code>repeated .presage.physiology.Point2dFloat value = 2;</code>
     */
    java.util.List<com.presage.physiology.proto.PointTypes.Point2dFloat> 
        getValueList();
    /**
     * <code>repeated .presage.physiology.Point2dFloat value = 2;</code>
     */
    com.presage.physiology.proto.PointTypes.Point2dFloat getValue(int index);
    /**
     * <code>repeated .presage.physiology.Point2dFloat value = 2;</code>
     */
    int getValueCount();

    /**
     * <code>bool stable = 3;</code>
     * @return The stable.
     */
    boolean getStable();
  }
  /**
   * Protobuf type {@code presage.physiology.Landmarks}
   */
  public  static final class Landmarks extends
      com.google.protobuf.GeneratedMessageLite<
          Landmarks, Landmarks.Builder> implements
      // @@protoc_insertion_point(message_implements:presage.physiology.Landmarks)
      LandmarksOrBuilder {
    private Landmarks() {
      value_ = emptyProtobufList();
    }
    public static final int TIME_FIELD_NUMBER = 1;
    private float time_;
    /**
     * <code>float time = 1;</code>
     * @return The time.
     */
    @java.lang.Override
    public float getTime() {
      return time_;
    }
    /**
     * <code>float time = 1;</code>
     * @param value The time to set.
     */
    private void setTime(float value) {
      
      time_ = value;
    }
    /**
     * <code>float time = 1;</code>
     */
    private void clearTime() {
      
      time_ = 0F;
    }

    public static final int VALUE_FIELD_NUMBER = 2;
    private com.google.protobuf.Internal.ProtobufList<com.presage.physiology.proto.PointTypes.Point2dFloat> value_;
    /**
     * <code>repeated .presage.physiology.Point2dFloat value = 2;</code>
     */
    @java.lang.Override
    public java.util.List<com.presage.physiology.proto.PointTypes.Point2dFloat> getValueList() {
      return value_;
    }
    /**
     * <code>repeated .presage.physiology.Point2dFloat value = 2;</code>
     */
    public java.util.List<? extends com.presage.physiology.proto.PointTypes.Point2dFloatOrBuilder> 
        getValueOrBuilderList() {
      return value_;
    }
    /**
     * <code>repeated .presage.physiology.Point2dFloat value = 2;</code>
     */
    @java.lang.Override
    public int getValueCount() {
      return value_.size();
    }
    /**
     * <code>repeated .presage.physiology.Point2dFloat value = 2;</code>
     */
    @java.lang.Override
    public com.presage.physiology.proto.PointTypes.Point2dFloat getValue(int index) {
      return value_.get(index);
    }
    /**
     * <code>repeated .presage.physiology.Point2dFloat value = 2;</code>
     */
    public com.presage.physiology.proto.PointTypes.Point2dFloatOrBuilder getValueOrBuilder(
        int index) {
      return value_.get(index);
    }
    private void ensureValueIsMutable() {
      com.google.protobuf.Internal.ProtobufList<com.presage.physiology.proto.PointTypes.Point2dFloat> tmp = value_;
      if (!tmp.isModifiable()) {
        value_ =
            com.google.protobuf.GeneratedMessageLite.mutableCopy(tmp);
       }
    }

    /**
     * <code>repeated .presage.physiology.Point2dFloat value = 2;</code>
     */
    private void setValue(
        int index, com.presage.physiology.proto.PointTypes.Point2dFloat value) {
      value.getClass();
  ensureValueIsMutable();
      value_.set(index, value);
    }
    /**
     * <code>repeated .presage.physiology.Point2dFloat value = 2;</code>
     */
    private void addValue(com.presage.physiology.proto.PointTypes.Point2dFloat value) {
      value.getClass();
  ensureValueIsMutable();
      value_.add(value);
    }
    /**
     * <code>repeated .presage.physiology.Point2dFloat value = 2;</code>
     */
    private void addValue(
        int index, com.presage.physiology.proto.PointTypes.Point2dFloat value) {
      value.getClass();
  ensureValueIsMutable();
      value_.add(index, value);
    }
    /**
     * <code>repeated .presage.physiology.Point2dFloat value = 2;</code>
     */
    private void addAllValue(
        java.lang.Iterable<? extends com.presage.physiology.proto.PointTypes.Point2dFloat> values) {
      ensureValueIsMutable();
      com.google.protobuf.AbstractMessageLite.addAll(
          values, value_);
    }
    /**
     * <code>repeated .presage.physiology.Point2dFloat value = 2;</code>
     */
    private void clearValue() {
      value_ = emptyProtobufList();
    }
    /**
     * <code>repeated .presage.physiology.Point2dFloat value = 2;</code>
     */
    private void removeValue(int index) {
      ensureValueIsMutable();
      value_.remove(index);
    }

    public static final int STABLE_FIELD_NUMBER = 3;
    private boolean stable_;
    /**
     * <code>bool stable = 3;</code>
     * @return The stable.
     */
    @java.lang.Override
    public boolean getStable() {
      return stable_;
    }
    /**
     * <code>bool stable = 3;</code>
     * @param value The stable to set.
     */
    private void setStable(boolean value) {
      
      stable_ = value;
    }
    /**
     * <code>bool stable = 3;</code>
     */
    private void clearStable() {
      
      stable_ = false;
    }

    public static com.presage.physiology.proto.MetricsProto.Landmarks parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.MetricsProto.Landmarks parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.Landmarks parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.MetricsProto.Landmarks parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.Landmarks parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.MetricsProto.Landmarks parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.Landmarks parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.MetricsProto.Landmarks parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.Landmarks parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.MetricsProto.Landmarks parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.Landmarks parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.MetricsProto.Landmarks parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }

    public static Builder newBuilder() {
      return (Builder) DEFAULT_INSTANCE.createBuilder();
    }
    public static Builder newBuilder(com.presage.physiology.proto.MetricsProto.Landmarks prototype) {
      return (Builder) DEFAULT_INSTANCE.createBuilder(prototype);
    }

    /**
     * Protobuf type {@code presage.physiology.Landmarks}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageLite.Builder<
          com.presage.physiology.proto.MetricsProto.Landmarks, Builder> implements
        // @@protoc_insertion_point(builder_implements:presage.physiology.Landmarks)
        com.presage.physiology.proto.MetricsProto.LandmarksOrBuilder {
      // Construct using com.presage.physiology.proto.MetricsProto.Landmarks.newBuilder()
      private Builder() {
        super(DEFAULT_INSTANCE);
      }


      /**
       * <code>float time = 1;</code>
       * @return The time.
       */
      @java.lang.Override
      public float getTime() {
        return instance.getTime();
      }
      /**
       * <code>float time = 1;</code>
       * @param value The time to set.
       * @return This builder for chaining.
       */
      public Builder setTime(float value) {
        copyOnWrite();
        instance.setTime(value);
        return this;
      }
      /**
       * <code>float time = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTime() {
        copyOnWrite();
        instance.clearTime();
        return this;
      }

      /**
       * <code>repeated .presage.physiology.Point2dFloat value = 2;</code>
       */
      @java.lang.Override
      public java.util.List<com.presage.physiology.proto.PointTypes.Point2dFloat> getValueList() {
        return java.util.Collections.unmodifiableList(
            instance.getValueList());
      }
      /**
       * <code>repeated .presage.physiology.Point2dFloat value = 2;</code>
       */
      @java.lang.Override
      public int getValueCount() {
        return instance.getValueCount();
      }/**
       * <code>repeated .presage.physiology.Point2dFloat value = 2;</code>
       */
      @java.lang.Override
      public com.presage.physiology.proto.PointTypes.Point2dFloat getValue(int index) {
        return instance.getValue(index);
      }
      /**
       * <code>repeated .presage.physiology.Point2dFloat value = 2;</code>
       */
      public Builder setValue(
          int index, com.presage.physiology.proto.PointTypes.Point2dFloat value) {
        copyOnWrite();
        instance.setValue(index, value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Point2dFloat value = 2;</code>
       */
      public Builder setValue(
          int index, com.presage.physiology.proto.PointTypes.Point2dFloat.Builder builderForValue) {
        copyOnWrite();
        instance.setValue(index,
            builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Point2dFloat value = 2;</code>
       */
      public Builder addValue(com.presage.physiology.proto.PointTypes.Point2dFloat value) {
        copyOnWrite();
        instance.addValue(value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Point2dFloat value = 2;</code>
       */
      public Builder addValue(
          int index, com.presage.physiology.proto.PointTypes.Point2dFloat value) {
        copyOnWrite();
        instance.addValue(index, value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Point2dFloat value = 2;</code>
       */
      public Builder addValue(
          com.presage.physiology.proto.PointTypes.Point2dFloat.Builder builderForValue) {
        copyOnWrite();
        instance.addValue(builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Point2dFloat value = 2;</code>
       */
      public Builder addValue(
          int index, com.presage.physiology.proto.PointTypes.Point2dFloat.Builder builderForValue) {
        copyOnWrite();
        instance.addValue(index,
            builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Point2dFloat value = 2;</code>
       */
      public Builder addAllValue(
          java.lang.Iterable<? extends com.presage.physiology.proto.PointTypes.Point2dFloat> values) {
        copyOnWrite();
        instance.addAllValue(values);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Point2dFloat value = 2;</code>
       */
      public Builder clearValue() {
        copyOnWrite();
        instance.clearValue();
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Point2dFloat value = 2;</code>
       */
      public Builder removeValue(int index) {
        copyOnWrite();
        instance.removeValue(index);
        return this;
      }

      /**
       * <code>bool stable = 3;</code>
       * @return The stable.
       */
      @java.lang.Override
      public boolean getStable() {
        return instance.getStable();
      }
      /**
       * <code>bool stable = 3;</code>
       * @param value The stable to set.
       * @return This builder for chaining.
       */
      public Builder setStable(boolean value) {
        copyOnWrite();
        instance.setStable(value);
        return this;
      }
      /**
       * <code>bool stable = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearStable() {
        copyOnWrite();
        instance.clearStable();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:presage.physiology.Landmarks)
    }
    @java.lang.Override
    @java.lang.SuppressWarnings({"unchecked", "fallthrough"})
    protected final java.lang.Object dynamicMethod(
        com.google.protobuf.GeneratedMessageLite.MethodToInvoke method,
        java.lang.Object arg0, java.lang.Object arg1) {
      switch (method) {
        case NEW_MUTABLE_INSTANCE: {
          return new com.presage.physiology.proto.MetricsProto.Landmarks();
        }
        case NEW_BUILDER: {
          return new Builder();
        }
        case BUILD_MESSAGE_INFO: {
            java.lang.Object[] objects = new java.lang.Object[] {
              "time_",
              "value_",
              com.presage.physiology.proto.PointTypes.Point2dFloat.class,
              "stable_",
            };
            java.lang.String info =
                "\u0000\u0003\u0000\u0000\u0001\u0003\u0003\u0000\u0001\u0000\u0001\u0001\u0002\u001b" +
                "\u0003\u0007";
            return newMessageInfo(DEFAULT_INSTANCE, info, objects);
        }
        // fall through
        case GET_DEFAULT_INSTANCE: {
          return DEFAULT_INSTANCE;
        }
        case GET_PARSER: {
          com.google.protobuf.Parser<com.presage.physiology.proto.MetricsProto.Landmarks> parser = PARSER;
          if (parser == null) {
            synchronized (com.presage.physiology.proto.MetricsProto.Landmarks.class) {
              parser = PARSER;
              if (parser == null) {
                parser =
                    new DefaultInstanceBasedParser<com.presage.physiology.proto.MetricsProto.Landmarks>(
                        DEFAULT_INSTANCE);
                PARSER = parser;
              }
            }
          }
          return parser;
      }
      case GET_MEMOIZED_IS_INITIALIZED: {
        return (byte) 1;
      }
      case SET_MEMOIZED_IS_INITIALIZED: {
        return null;
      }
      }
      throw new UnsupportedOperationException();
    }


    // @@protoc_insertion_point(class_scope:presage.physiology.Landmarks)
    private static final com.presage.physiology.proto.MetricsProto.Landmarks DEFAULT_INSTANCE;
    static {
      Landmarks defaultInstance = new Landmarks();
      // New instances are implicitly immutable so no need to make
      // immutable.
      DEFAULT_INSTANCE = defaultInstance;
      com.google.protobuf.GeneratedMessageLite.registerDefaultInstance(
        Landmarks.class, defaultInstance);
    }

    public static com.presage.physiology.proto.MetricsProto.Landmarks getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static volatile com.google.protobuf.Parser<Landmarks> PARSER;

    public static com.google.protobuf.Parser<Landmarks> parser() {
      return DEFAULT_INSTANCE.getParserForType();
    }
  }

  public interface FaceOrBuilder extends
      // @@protoc_insertion_point(interface_extends:presage.physiology.Face)
      com.google.protobuf.MessageLiteOrBuilder {

    /**
     * <code>repeated .presage.physiology.DetectionStatus blinking = 1;</code>
     */
    java.util.List<com.presage.physiology.proto.MetricsProto.DetectionStatus> 
        getBlinkingList();
    /**
     * <code>repeated .presage.physiology.DetectionStatus blinking = 1;</code>
     */
    com.presage.physiology.proto.MetricsProto.DetectionStatus getBlinking(int index);
    /**
     * <code>repeated .presage.physiology.DetectionStatus blinking = 1;</code>
     */
    int getBlinkingCount();

    /**
     * <code>repeated .presage.physiology.DetectionStatus talking = 2;</code>
     */
    java.util.List<com.presage.physiology.proto.MetricsProto.DetectionStatus> 
        getTalkingList();
    /**
     * <code>repeated .presage.physiology.DetectionStatus talking = 2;</code>
     */
    com.presage.physiology.proto.MetricsProto.DetectionStatus getTalking(int index);
    /**
     * <code>repeated .presage.physiology.DetectionStatus talking = 2;</code>
     */
    int getTalkingCount();

    /**
     * <code>repeated .presage.physiology.Landmarks landmarks = 3;</code>
     */
    java.util.List<com.presage.physiology.proto.MetricsProto.Landmarks> 
        getLandmarksList();
    /**
     * <code>repeated .presage.physiology.Landmarks landmarks = 3;</code>
     */
    com.presage.physiology.proto.MetricsProto.Landmarks getLandmarks(int index);
    /**
     * <code>repeated .presage.physiology.Landmarks landmarks = 3;</code>
     */
    int getLandmarksCount();
  }
  /**
   * Protobuf type {@code presage.physiology.Face}
   */
  public  static final class Face extends
      com.google.protobuf.GeneratedMessageLite<
          Face, Face.Builder> implements
      // @@protoc_insertion_point(message_implements:presage.physiology.Face)
      FaceOrBuilder {
    private Face() {
      blinking_ = emptyProtobufList();
      talking_ = emptyProtobufList();
      landmarks_ = emptyProtobufList();
    }
    public static final int BLINKING_FIELD_NUMBER = 1;
    private com.google.protobuf.Internal.ProtobufList<com.presage.physiology.proto.MetricsProto.DetectionStatus> blinking_;
    /**
     * <code>repeated .presage.physiology.DetectionStatus blinking = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.presage.physiology.proto.MetricsProto.DetectionStatus> getBlinkingList() {
      return blinking_;
    }
    /**
     * <code>repeated .presage.physiology.DetectionStatus blinking = 1;</code>
     */
    public java.util.List<? extends com.presage.physiology.proto.MetricsProto.DetectionStatusOrBuilder> 
        getBlinkingOrBuilderList() {
      return blinking_;
    }
    /**
     * <code>repeated .presage.physiology.DetectionStatus blinking = 1;</code>
     */
    @java.lang.Override
    public int getBlinkingCount() {
      return blinking_.size();
    }
    /**
     * <code>repeated .presage.physiology.DetectionStatus blinking = 1;</code>
     */
    @java.lang.Override
    public com.presage.physiology.proto.MetricsProto.DetectionStatus getBlinking(int index) {
      return blinking_.get(index);
    }
    /**
     * <code>repeated .presage.physiology.DetectionStatus blinking = 1;</code>
     */
    public com.presage.physiology.proto.MetricsProto.DetectionStatusOrBuilder getBlinkingOrBuilder(
        int index) {
      return blinking_.get(index);
    }
    private void ensureBlinkingIsMutable() {
      com.google.protobuf.Internal.ProtobufList<com.presage.physiology.proto.MetricsProto.DetectionStatus> tmp = blinking_;
      if (!tmp.isModifiable()) {
        blinking_ =
            com.google.protobuf.GeneratedMessageLite.mutableCopy(tmp);
       }
    }

    /**
     * <code>repeated .presage.physiology.DetectionStatus blinking = 1;</code>
     */
    private void setBlinking(
        int index, com.presage.physiology.proto.MetricsProto.DetectionStatus value) {
      value.getClass();
  ensureBlinkingIsMutable();
      blinking_.set(index, value);
    }
    /**
     * <code>repeated .presage.physiology.DetectionStatus blinking = 1;</code>
     */
    private void addBlinking(com.presage.physiology.proto.MetricsProto.DetectionStatus value) {
      value.getClass();
  ensureBlinkingIsMutable();
      blinking_.add(value);
    }
    /**
     * <code>repeated .presage.physiology.DetectionStatus blinking = 1;</code>
     */
    private void addBlinking(
        int index, com.presage.physiology.proto.MetricsProto.DetectionStatus value) {
      value.getClass();
  ensureBlinkingIsMutable();
      blinking_.add(index, value);
    }
    /**
     * <code>repeated .presage.physiology.DetectionStatus blinking = 1;</code>
     */
    private void addAllBlinking(
        java.lang.Iterable<? extends com.presage.physiology.proto.MetricsProto.DetectionStatus> values) {
      ensureBlinkingIsMutable();
      com.google.protobuf.AbstractMessageLite.addAll(
          values, blinking_);
    }
    /**
     * <code>repeated .presage.physiology.DetectionStatus blinking = 1;</code>
     */
    private void clearBlinking() {
      blinking_ = emptyProtobufList();
    }
    /**
     * <code>repeated .presage.physiology.DetectionStatus blinking = 1;</code>
     */
    private void removeBlinking(int index) {
      ensureBlinkingIsMutable();
      blinking_.remove(index);
    }

    public static final int TALKING_FIELD_NUMBER = 2;
    private com.google.protobuf.Internal.ProtobufList<com.presage.physiology.proto.MetricsProto.DetectionStatus> talking_;
    /**
     * <code>repeated .presage.physiology.DetectionStatus talking = 2;</code>
     */
    @java.lang.Override
    public java.util.List<com.presage.physiology.proto.MetricsProto.DetectionStatus> getTalkingList() {
      return talking_;
    }
    /**
     * <code>repeated .presage.physiology.DetectionStatus talking = 2;</code>
     */
    public java.util.List<? extends com.presage.physiology.proto.MetricsProto.DetectionStatusOrBuilder> 
        getTalkingOrBuilderList() {
      return talking_;
    }
    /**
     * <code>repeated .presage.physiology.DetectionStatus talking = 2;</code>
     */
    @java.lang.Override
    public int getTalkingCount() {
      return talking_.size();
    }
    /**
     * <code>repeated .presage.physiology.DetectionStatus talking = 2;</code>
     */
    @java.lang.Override
    public com.presage.physiology.proto.MetricsProto.DetectionStatus getTalking(int index) {
      return talking_.get(index);
    }
    /**
     * <code>repeated .presage.physiology.DetectionStatus talking = 2;</code>
     */
    public com.presage.physiology.proto.MetricsProto.DetectionStatusOrBuilder getTalkingOrBuilder(
        int index) {
      return talking_.get(index);
    }
    private void ensureTalkingIsMutable() {
      com.google.protobuf.Internal.ProtobufList<com.presage.physiology.proto.MetricsProto.DetectionStatus> tmp = talking_;
      if (!tmp.isModifiable()) {
        talking_ =
            com.google.protobuf.GeneratedMessageLite.mutableCopy(tmp);
       }
    }

    /**
     * <code>repeated .presage.physiology.DetectionStatus talking = 2;</code>
     */
    private void setTalking(
        int index, com.presage.physiology.proto.MetricsProto.DetectionStatus value) {
      value.getClass();
  ensureTalkingIsMutable();
      talking_.set(index, value);
    }
    /**
     * <code>repeated .presage.physiology.DetectionStatus talking = 2;</code>
     */
    private void addTalking(com.presage.physiology.proto.MetricsProto.DetectionStatus value) {
      value.getClass();
  ensureTalkingIsMutable();
      talking_.add(value);
    }
    /**
     * <code>repeated .presage.physiology.DetectionStatus talking = 2;</code>
     */
    private void addTalking(
        int index, com.presage.physiology.proto.MetricsProto.DetectionStatus value) {
      value.getClass();
  ensureTalkingIsMutable();
      talking_.add(index, value);
    }
    /**
     * <code>repeated .presage.physiology.DetectionStatus talking = 2;</code>
     */
    private void addAllTalking(
        java.lang.Iterable<? extends com.presage.physiology.proto.MetricsProto.DetectionStatus> values) {
      ensureTalkingIsMutable();
      com.google.protobuf.AbstractMessageLite.addAll(
          values, talking_);
    }
    /**
     * <code>repeated .presage.physiology.DetectionStatus talking = 2;</code>
     */
    private void clearTalking() {
      talking_ = emptyProtobufList();
    }
    /**
     * <code>repeated .presage.physiology.DetectionStatus talking = 2;</code>
     */
    private void removeTalking(int index) {
      ensureTalkingIsMutable();
      talking_.remove(index);
    }

    public static final int LANDMARKS_FIELD_NUMBER = 3;
    private com.google.protobuf.Internal.ProtobufList<com.presage.physiology.proto.MetricsProto.Landmarks> landmarks_;
    /**
     * <code>repeated .presage.physiology.Landmarks landmarks = 3;</code>
     */
    @java.lang.Override
    public java.util.List<com.presage.physiology.proto.MetricsProto.Landmarks> getLandmarksList() {
      return landmarks_;
    }
    /**
     * <code>repeated .presage.physiology.Landmarks landmarks = 3;</code>
     */
    public java.util.List<? extends com.presage.physiology.proto.MetricsProto.LandmarksOrBuilder> 
        getLandmarksOrBuilderList() {
      return landmarks_;
    }
    /**
     * <code>repeated .presage.physiology.Landmarks landmarks = 3;</code>
     */
    @java.lang.Override
    public int getLandmarksCount() {
      return landmarks_.size();
    }
    /**
     * <code>repeated .presage.physiology.Landmarks landmarks = 3;</code>
     */
    @java.lang.Override
    public com.presage.physiology.proto.MetricsProto.Landmarks getLandmarks(int index) {
      return landmarks_.get(index);
    }
    /**
     * <code>repeated .presage.physiology.Landmarks landmarks = 3;</code>
     */
    public com.presage.physiology.proto.MetricsProto.LandmarksOrBuilder getLandmarksOrBuilder(
        int index) {
      return landmarks_.get(index);
    }
    private void ensureLandmarksIsMutable() {
      com.google.protobuf.Internal.ProtobufList<com.presage.physiology.proto.MetricsProto.Landmarks> tmp = landmarks_;
      if (!tmp.isModifiable()) {
        landmarks_ =
            com.google.protobuf.GeneratedMessageLite.mutableCopy(tmp);
       }
    }

    /**
     * <code>repeated .presage.physiology.Landmarks landmarks = 3;</code>
     */
    private void setLandmarks(
        int index, com.presage.physiology.proto.MetricsProto.Landmarks value) {
      value.getClass();
  ensureLandmarksIsMutable();
      landmarks_.set(index, value);
    }
    /**
     * <code>repeated .presage.physiology.Landmarks landmarks = 3;</code>
     */
    private void addLandmarks(com.presage.physiology.proto.MetricsProto.Landmarks value) {
      value.getClass();
  ensureLandmarksIsMutable();
      landmarks_.add(value);
    }
    /**
     * <code>repeated .presage.physiology.Landmarks landmarks = 3;</code>
     */
    private void addLandmarks(
        int index, com.presage.physiology.proto.MetricsProto.Landmarks value) {
      value.getClass();
  ensureLandmarksIsMutable();
      landmarks_.add(index, value);
    }
    /**
     * <code>repeated .presage.physiology.Landmarks landmarks = 3;</code>
     */
    private void addAllLandmarks(
        java.lang.Iterable<? extends com.presage.physiology.proto.MetricsProto.Landmarks> values) {
      ensureLandmarksIsMutable();
      com.google.protobuf.AbstractMessageLite.addAll(
          values, landmarks_);
    }
    /**
     * <code>repeated .presage.physiology.Landmarks landmarks = 3;</code>
     */
    private void clearLandmarks() {
      landmarks_ = emptyProtobufList();
    }
    /**
     * <code>repeated .presage.physiology.Landmarks landmarks = 3;</code>
     */
    private void removeLandmarks(int index) {
      ensureLandmarksIsMutable();
      landmarks_.remove(index);
    }

    public static com.presage.physiology.proto.MetricsProto.Face parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.MetricsProto.Face parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.Face parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.MetricsProto.Face parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.Face parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.MetricsProto.Face parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.Face parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.MetricsProto.Face parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.Face parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.MetricsProto.Face parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.Face parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.MetricsProto.Face parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }

    public static Builder newBuilder() {
      return (Builder) DEFAULT_INSTANCE.createBuilder();
    }
    public static Builder newBuilder(com.presage.physiology.proto.MetricsProto.Face prototype) {
      return (Builder) DEFAULT_INSTANCE.createBuilder(prototype);
    }

    /**
     * Protobuf type {@code presage.physiology.Face}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageLite.Builder<
          com.presage.physiology.proto.MetricsProto.Face, Builder> implements
        // @@protoc_insertion_point(builder_implements:presage.physiology.Face)
        com.presage.physiology.proto.MetricsProto.FaceOrBuilder {
      // Construct using com.presage.physiology.proto.MetricsProto.Face.newBuilder()
      private Builder() {
        super(DEFAULT_INSTANCE);
      }


      /**
       * <code>repeated .presage.physiology.DetectionStatus blinking = 1;</code>
       */
      @java.lang.Override
      public java.util.List<com.presage.physiology.proto.MetricsProto.DetectionStatus> getBlinkingList() {
        return java.util.Collections.unmodifiableList(
            instance.getBlinkingList());
      }
      /**
       * <code>repeated .presage.physiology.DetectionStatus blinking = 1;</code>
       */
      @java.lang.Override
      public int getBlinkingCount() {
        return instance.getBlinkingCount();
      }/**
       * <code>repeated .presage.physiology.DetectionStatus blinking = 1;</code>
       */
      @java.lang.Override
      public com.presage.physiology.proto.MetricsProto.DetectionStatus getBlinking(int index) {
        return instance.getBlinking(index);
      }
      /**
       * <code>repeated .presage.physiology.DetectionStatus blinking = 1;</code>
       */
      public Builder setBlinking(
          int index, com.presage.physiology.proto.MetricsProto.DetectionStatus value) {
        copyOnWrite();
        instance.setBlinking(index, value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.DetectionStatus blinking = 1;</code>
       */
      public Builder setBlinking(
          int index, com.presage.physiology.proto.MetricsProto.DetectionStatus.Builder builderForValue) {
        copyOnWrite();
        instance.setBlinking(index,
            builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.DetectionStatus blinking = 1;</code>
       */
      public Builder addBlinking(com.presage.physiology.proto.MetricsProto.DetectionStatus value) {
        copyOnWrite();
        instance.addBlinking(value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.DetectionStatus blinking = 1;</code>
       */
      public Builder addBlinking(
          int index, com.presage.physiology.proto.MetricsProto.DetectionStatus value) {
        copyOnWrite();
        instance.addBlinking(index, value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.DetectionStatus blinking = 1;</code>
       */
      public Builder addBlinking(
          com.presage.physiology.proto.MetricsProto.DetectionStatus.Builder builderForValue) {
        copyOnWrite();
        instance.addBlinking(builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.DetectionStatus blinking = 1;</code>
       */
      public Builder addBlinking(
          int index, com.presage.physiology.proto.MetricsProto.DetectionStatus.Builder builderForValue) {
        copyOnWrite();
        instance.addBlinking(index,
            builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.DetectionStatus blinking = 1;</code>
       */
      public Builder addAllBlinking(
          java.lang.Iterable<? extends com.presage.physiology.proto.MetricsProto.DetectionStatus> values) {
        copyOnWrite();
        instance.addAllBlinking(values);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.DetectionStatus blinking = 1;</code>
       */
      public Builder clearBlinking() {
        copyOnWrite();
        instance.clearBlinking();
        return this;
      }
      /**
       * <code>repeated .presage.physiology.DetectionStatus blinking = 1;</code>
       */
      public Builder removeBlinking(int index) {
        copyOnWrite();
        instance.removeBlinking(index);
        return this;
      }

      /**
       * <code>repeated .presage.physiology.DetectionStatus talking = 2;</code>
       */
      @java.lang.Override
      public java.util.List<com.presage.physiology.proto.MetricsProto.DetectionStatus> getTalkingList() {
        return java.util.Collections.unmodifiableList(
            instance.getTalkingList());
      }
      /**
       * <code>repeated .presage.physiology.DetectionStatus talking = 2;</code>
       */
      @java.lang.Override
      public int getTalkingCount() {
        return instance.getTalkingCount();
      }/**
       * <code>repeated .presage.physiology.DetectionStatus talking = 2;</code>
       */
      @java.lang.Override
      public com.presage.physiology.proto.MetricsProto.DetectionStatus getTalking(int index) {
        return instance.getTalking(index);
      }
      /**
       * <code>repeated .presage.physiology.DetectionStatus talking = 2;</code>
       */
      public Builder setTalking(
          int index, com.presage.physiology.proto.MetricsProto.DetectionStatus value) {
        copyOnWrite();
        instance.setTalking(index, value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.DetectionStatus talking = 2;</code>
       */
      public Builder setTalking(
          int index, com.presage.physiology.proto.MetricsProto.DetectionStatus.Builder builderForValue) {
        copyOnWrite();
        instance.setTalking(index,
            builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.DetectionStatus talking = 2;</code>
       */
      public Builder addTalking(com.presage.physiology.proto.MetricsProto.DetectionStatus value) {
        copyOnWrite();
        instance.addTalking(value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.DetectionStatus talking = 2;</code>
       */
      public Builder addTalking(
          int index, com.presage.physiology.proto.MetricsProto.DetectionStatus value) {
        copyOnWrite();
        instance.addTalking(index, value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.DetectionStatus talking = 2;</code>
       */
      public Builder addTalking(
          com.presage.physiology.proto.MetricsProto.DetectionStatus.Builder builderForValue) {
        copyOnWrite();
        instance.addTalking(builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.DetectionStatus talking = 2;</code>
       */
      public Builder addTalking(
          int index, com.presage.physiology.proto.MetricsProto.DetectionStatus.Builder builderForValue) {
        copyOnWrite();
        instance.addTalking(index,
            builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.DetectionStatus talking = 2;</code>
       */
      public Builder addAllTalking(
          java.lang.Iterable<? extends com.presage.physiology.proto.MetricsProto.DetectionStatus> values) {
        copyOnWrite();
        instance.addAllTalking(values);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.DetectionStatus talking = 2;</code>
       */
      public Builder clearTalking() {
        copyOnWrite();
        instance.clearTalking();
        return this;
      }
      /**
       * <code>repeated .presage.physiology.DetectionStatus talking = 2;</code>
       */
      public Builder removeTalking(int index) {
        copyOnWrite();
        instance.removeTalking(index);
        return this;
      }

      /**
       * <code>repeated .presage.physiology.Landmarks landmarks = 3;</code>
       */
      @java.lang.Override
      public java.util.List<com.presage.physiology.proto.MetricsProto.Landmarks> getLandmarksList() {
        return java.util.Collections.unmodifiableList(
            instance.getLandmarksList());
      }
      /**
       * <code>repeated .presage.physiology.Landmarks landmarks = 3;</code>
       */
      @java.lang.Override
      public int getLandmarksCount() {
        return instance.getLandmarksCount();
      }/**
       * <code>repeated .presage.physiology.Landmarks landmarks = 3;</code>
       */
      @java.lang.Override
      public com.presage.physiology.proto.MetricsProto.Landmarks getLandmarks(int index) {
        return instance.getLandmarks(index);
      }
      /**
       * <code>repeated .presage.physiology.Landmarks landmarks = 3;</code>
       */
      public Builder setLandmarks(
          int index, com.presage.physiology.proto.MetricsProto.Landmarks value) {
        copyOnWrite();
        instance.setLandmarks(index, value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Landmarks landmarks = 3;</code>
       */
      public Builder setLandmarks(
          int index, com.presage.physiology.proto.MetricsProto.Landmarks.Builder builderForValue) {
        copyOnWrite();
        instance.setLandmarks(index,
            builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Landmarks landmarks = 3;</code>
       */
      public Builder addLandmarks(com.presage.physiology.proto.MetricsProto.Landmarks value) {
        copyOnWrite();
        instance.addLandmarks(value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Landmarks landmarks = 3;</code>
       */
      public Builder addLandmarks(
          int index, com.presage.physiology.proto.MetricsProto.Landmarks value) {
        copyOnWrite();
        instance.addLandmarks(index, value);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Landmarks landmarks = 3;</code>
       */
      public Builder addLandmarks(
          com.presage.physiology.proto.MetricsProto.Landmarks.Builder builderForValue) {
        copyOnWrite();
        instance.addLandmarks(builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Landmarks landmarks = 3;</code>
       */
      public Builder addLandmarks(
          int index, com.presage.physiology.proto.MetricsProto.Landmarks.Builder builderForValue) {
        copyOnWrite();
        instance.addLandmarks(index,
            builderForValue.build());
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Landmarks landmarks = 3;</code>
       */
      public Builder addAllLandmarks(
          java.lang.Iterable<? extends com.presage.physiology.proto.MetricsProto.Landmarks> values) {
        copyOnWrite();
        instance.addAllLandmarks(values);
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Landmarks landmarks = 3;</code>
       */
      public Builder clearLandmarks() {
        copyOnWrite();
        instance.clearLandmarks();
        return this;
      }
      /**
       * <code>repeated .presage.physiology.Landmarks landmarks = 3;</code>
       */
      public Builder removeLandmarks(int index) {
        copyOnWrite();
        instance.removeLandmarks(index);
        return this;
      }

      // @@protoc_insertion_point(builder_scope:presage.physiology.Face)
    }
    @java.lang.Override
    @java.lang.SuppressWarnings({"unchecked", "fallthrough"})
    protected final java.lang.Object dynamicMethod(
        com.google.protobuf.GeneratedMessageLite.MethodToInvoke method,
        java.lang.Object arg0, java.lang.Object arg1) {
      switch (method) {
        case NEW_MUTABLE_INSTANCE: {
          return new com.presage.physiology.proto.MetricsProto.Face();
        }
        case NEW_BUILDER: {
          return new Builder();
        }
        case BUILD_MESSAGE_INFO: {
            java.lang.Object[] objects = new java.lang.Object[] {
              "blinking_",
              com.presage.physiology.proto.MetricsProto.DetectionStatus.class,
              "talking_",
              com.presage.physiology.proto.MetricsProto.DetectionStatus.class,
              "landmarks_",
              com.presage.physiology.proto.MetricsProto.Landmarks.class,
            };
            java.lang.String info =
                "\u0000\u0003\u0000\u0000\u0001\u0003\u0003\u0000\u0003\u0000\u0001\u001b\u0002\u001b" +
                "\u0003\u001b";
            return newMessageInfo(DEFAULT_INSTANCE, info, objects);
        }
        // fall through
        case GET_DEFAULT_INSTANCE: {
          return DEFAULT_INSTANCE;
        }
        case GET_PARSER: {
          com.google.protobuf.Parser<com.presage.physiology.proto.MetricsProto.Face> parser = PARSER;
          if (parser == null) {
            synchronized (com.presage.physiology.proto.MetricsProto.Face.class) {
              parser = PARSER;
              if (parser == null) {
                parser =
                    new DefaultInstanceBasedParser<com.presage.physiology.proto.MetricsProto.Face>(
                        DEFAULT_INSTANCE);
                PARSER = parser;
              }
            }
          }
          return parser;
      }
      case GET_MEMOIZED_IS_INITIALIZED: {
        return (byte) 1;
      }
      case SET_MEMOIZED_IS_INITIALIZED: {
        return null;
      }
      }
      throw new UnsupportedOperationException();
    }


    // @@protoc_insertion_point(class_scope:presage.physiology.Face)
    private static final com.presage.physiology.proto.MetricsProto.Face DEFAULT_INSTANCE;
    static {
      Face defaultInstance = new Face();
      // New instances are implicitly immutable so no need to make
      // immutable.
      DEFAULT_INSTANCE = defaultInstance;
      com.google.protobuf.GeneratedMessageLite.registerDefaultInstance(
        Face.class, defaultInstance);
    }

    public static com.presage.physiology.proto.MetricsProto.Face getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static volatile com.google.protobuf.Parser<Face> PARSER;

    public static com.google.protobuf.Parser<Face> parser() {
      return DEFAULT_INSTANCE.getParserForType();
    }
  }

  public interface MetadataOrBuilder extends
      // @@protoc_insertion_point(interface_extends:presage.physiology.Metadata)
      com.google.protobuf.MessageLiteOrBuilder {

    /**
     * <code>string id = 1;</code>
     * @return The id.
     */
    java.lang.String getId();
    /**
     * <code>string id = 1;</code>
     * @return The bytes for id.
     */
    com.google.protobuf.ByteString
        getIdBytes();

    /**
     * <code>string upload_timestamp = 2;</code>
     * @return The uploadTimestamp.
     */
    java.lang.String getUploadTimestamp();
    /**
     * <code>string upload_timestamp = 2;</code>
     * @return The bytes for uploadTimestamp.
     */
    com.google.protobuf.ByteString
        getUploadTimestampBytes();

    /**
     * <code>string api_version = 3;</code>
     * @return The apiVersion.
     */
    java.lang.String getApiVersion();
    /**
     * <code>string api_version = 3;</code>
     * @return The bytes for apiVersion.
     */
    com.google.protobuf.ByteString
        getApiVersionBytes();

    /**
     * <pre>
     * seconds since Linux epoch
     * </pre>
     *
     * <code>double sent_at_s = 4;</code>
     * @return The sentAtS.
     */
    double getSentAtS();

    /**
     * <pre>
     * Microseconds since start of capture, should be associated w/ PreprocessedDataBuffer that triggered
     * the generation of this MetricsBuffer object
     * </pre>
     *
     * <code>int64 frame_timestamp = 5;</code>
     * @return The frameTimestamp.
     */
    long getFrameTimestamp();

    /**
     * <pre>
     * should be set to number of actual image frames used to generate PreprocessedDataBuffer
     * that triggered the generation of this MetricsBuffer object, excluding those frames that were partially used for
     * previous buffers
     * </pre>
     *
     * <code>int32 frame_count = 6;</code>
     * @return The frameCount.
     */
    int getFrameCount();
  }
  /**
   * Protobuf type {@code presage.physiology.Metadata}
   */
  public  static final class Metadata extends
      com.google.protobuf.GeneratedMessageLite<
          Metadata, Metadata.Builder> implements
      // @@protoc_insertion_point(message_implements:presage.physiology.Metadata)
      MetadataOrBuilder {
    private Metadata() {
      id_ = "";
      uploadTimestamp_ = "";
      apiVersion_ = "";
    }
    public static final int ID_FIELD_NUMBER = 1;
    private java.lang.String id_;
    /**
     * <code>string id = 1;</code>
     * @return The id.
     */
    @java.lang.Override
    public java.lang.String getId() {
      return id_;
    }
    /**
     * <code>string id = 1;</code>
     * @return The bytes for id.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIdBytes() {
      return com.google.protobuf.ByteString.copyFromUtf8(id_);
    }
    /**
     * <code>string id = 1;</code>
     * @param value The id to set.
     */
    private void setId(
        java.lang.String value) {
      java.lang.Class<?> valueClass = value.getClass();
  
      id_ = value;
    }
    /**
     * <code>string id = 1;</code>
     */
    private void clearId() {
      
      id_ = getDefaultInstance().getId();
    }
    /**
     * <code>string id = 1;</code>
     * @param value The bytes for id to set.
     */
    private void setIdBytes(
        com.google.protobuf.ByteString value) {
      checkByteStringIsUtf8(value);
      id_ = value.toStringUtf8();
      
    }

    public static final int UPLOAD_TIMESTAMP_FIELD_NUMBER = 2;
    private java.lang.String uploadTimestamp_;
    /**
     * <code>string upload_timestamp = 2;</code>
     * @return The uploadTimestamp.
     */
    @java.lang.Override
    public java.lang.String getUploadTimestamp() {
      return uploadTimestamp_;
    }
    /**
     * <code>string upload_timestamp = 2;</code>
     * @return The bytes for uploadTimestamp.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getUploadTimestampBytes() {
      return com.google.protobuf.ByteString.copyFromUtf8(uploadTimestamp_);
    }
    /**
     * <code>string upload_timestamp = 2;</code>
     * @param value The uploadTimestamp to set.
     */
    private void setUploadTimestamp(
        java.lang.String value) {
      java.lang.Class<?> valueClass = value.getClass();
  
      uploadTimestamp_ = value;
    }
    /**
     * <code>string upload_timestamp = 2;</code>
     */
    private void clearUploadTimestamp() {
      
      uploadTimestamp_ = getDefaultInstance().getUploadTimestamp();
    }
    /**
     * <code>string upload_timestamp = 2;</code>
     * @param value The bytes for uploadTimestamp to set.
     */
    private void setUploadTimestampBytes(
        com.google.protobuf.ByteString value) {
      checkByteStringIsUtf8(value);
      uploadTimestamp_ = value.toStringUtf8();
      
    }

    public static final int API_VERSION_FIELD_NUMBER = 3;
    private java.lang.String apiVersion_;
    /**
     * <code>string api_version = 3;</code>
     * @return The apiVersion.
     */
    @java.lang.Override
    public java.lang.String getApiVersion() {
      return apiVersion_;
    }
    /**
     * <code>string api_version = 3;</code>
     * @return The bytes for apiVersion.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getApiVersionBytes() {
      return com.google.protobuf.ByteString.copyFromUtf8(apiVersion_);
    }
    /**
     * <code>string api_version = 3;</code>
     * @param value The apiVersion to set.
     */
    private void setApiVersion(
        java.lang.String value) {
      java.lang.Class<?> valueClass = value.getClass();
  
      apiVersion_ = value;
    }
    /**
     * <code>string api_version = 3;</code>
     */
    private void clearApiVersion() {
      
      apiVersion_ = getDefaultInstance().getApiVersion();
    }
    /**
     * <code>string api_version = 3;</code>
     * @param value The bytes for apiVersion to set.
     */
    private void setApiVersionBytes(
        com.google.protobuf.ByteString value) {
      checkByteStringIsUtf8(value);
      apiVersion_ = value.toStringUtf8();
      
    }

    public static final int SENT_AT_S_FIELD_NUMBER = 4;
    private double sentAtS_;
    /**
     * <pre>
     * seconds since Linux epoch
     * </pre>
     *
     * <code>double sent_at_s = 4;</code>
     * @return The sentAtS.
     */
    @java.lang.Override
    public double getSentAtS() {
      return sentAtS_;
    }
    /**
     * <pre>
     * seconds since Linux epoch
     * </pre>
     *
     * <code>double sent_at_s = 4;</code>
     * @param value The sentAtS to set.
     */
    private void setSentAtS(double value) {
      
      sentAtS_ = value;
    }
    /**
     * <pre>
     * seconds since Linux epoch
     * </pre>
     *
     * <code>double sent_at_s = 4;</code>
     */
    private void clearSentAtS() {
      
      sentAtS_ = 0D;
    }

    public static final int FRAME_TIMESTAMP_FIELD_NUMBER = 5;
    private long frameTimestamp_;
    /**
     * <pre>
     * Microseconds since start of capture, should be associated w/ PreprocessedDataBuffer that triggered
     * the generation of this MetricsBuffer object
     * </pre>
     *
     * <code>int64 frame_timestamp = 5;</code>
     * @return The frameTimestamp.
     */
    @java.lang.Override
    public long getFrameTimestamp() {
      return frameTimestamp_;
    }
    /**
     * <pre>
     * Microseconds since start of capture, should be associated w/ PreprocessedDataBuffer that triggered
     * the generation of this MetricsBuffer object
     * </pre>
     *
     * <code>int64 frame_timestamp = 5;</code>
     * @param value The frameTimestamp to set.
     */
    private void setFrameTimestamp(long value) {
      
      frameTimestamp_ = value;
    }
    /**
     * <pre>
     * Microseconds since start of capture, should be associated w/ PreprocessedDataBuffer that triggered
     * the generation of this MetricsBuffer object
     * </pre>
     *
     * <code>int64 frame_timestamp = 5;</code>
     */
    private void clearFrameTimestamp() {
      
      frameTimestamp_ = 0L;
    }

    public static final int FRAME_COUNT_FIELD_NUMBER = 6;
    private int frameCount_;
    /**
     * <pre>
     * should be set to number of actual image frames used to generate PreprocessedDataBuffer
     * that triggered the generation of this MetricsBuffer object, excluding those frames that were partially used for
     * previous buffers
     * </pre>
     *
     * <code>int32 frame_count = 6;</code>
     * @return The frameCount.
     */
    @java.lang.Override
    public int getFrameCount() {
      return frameCount_;
    }
    /**
     * <pre>
     * should be set to number of actual image frames used to generate PreprocessedDataBuffer
     * that triggered the generation of this MetricsBuffer object, excluding those frames that were partially used for
     * previous buffers
     * </pre>
     *
     * <code>int32 frame_count = 6;</code>
     * @param value The frameCount to set.
     */
    private void setFrameCount(int value) {
      
      frameCount_ = value;
    }
    /**
     * <pre>
     * should be set to number of actual image frames used to generate PreprocessedDataBuffer
     * that triggered the generation of this MetricsBuffer object, excluding those frames that were partially used for
     * previous buffers
     * </pre>
     *
     * <code>int32 frame_count = 6;</code>
     */
    private void clearFrameCount() {
      
      frameCount_ = 0;
    }

    public static com.presage.physiology.proto.MetricsProto.Metadata parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.MetricsProto.Metadata parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.Metadata parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.MetricsProto.Metadata parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.Metadata parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.MetricsProto.Metadata parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.Metadata parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.MetricsProto.Metadata parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.Metadata parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.MetricsProto.Metadata parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.Metadata parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.MetricsProto.Metadata parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }

    public static Builder newBuilder() {
      return (Builder) DEFAULT_INSTANCE.createBuilder();
    }
    public static Builder newBuilder(com.presage.physiology.proto.MetricsProto.Metadata prototype) {
      return (Builder) DEFAULT_INSTANCE.createBuilder(prototype);
    }

    /**
     * Protobuf type {@code presage.physiology.Metadata}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageLite.Builder<
          com.presage.physiology.proto.MetricsProto.Metadata, Builder> implements
        // @@protoc_insertion_point(builder_implements:presage.physiology.Metadata)
        com.presage.physiology.proto.MetricsProto.MetadataOrBuilder {
      // Construct using com.presage.physiology.proto.MetricsProto.Metadata.newBuilder()
      private Builder() {
        super(DEFAULT_INSTANCE);
      }


      /**
       * <code>string id = 1;</code>
       * @return The id.
       */
      @java.lang.Override
      public java.lang.String getId() {
        return instance.getId();
      }
      /**
       * <code>string id = 1;</code>
       * @return The bytes for id.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getIdBytes() {
        return instance.getIdBytes();
      }
      /**
       * <code>string id = 1;</code>
       * @param value The id to set.
       * @return This builder for chaining.
       */
      public Builder setId(
          java.lang.String value) {
        copyOnWrite();
        instance.setId(value);
        return this;
      }
      /**
       * <code>string id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearId() {
        copyOnWrite();
        instance.clearId();
        return this;
      }
      /**
       * <code>string id = 1;</code>
       * @param value The bytes for id to set.
       * @return This builder for chaining.
       */
      public Builder setIdBytes(
          com.google.protobuf.ByteString value) {
        copyOnWrite();
        instance.setIdBytes(value);
        return this;
      }

      /**
       * <code>string upload_timestamp = 2;</code>
       * @return The uploadTimestamp.
       */
      @java.lang.Override
      public java.lang.String getUploadTimestamp() {
        return instance.getUploadTimestamp();
      }
      /**
       * <code>string upload_timestamp = 2;</code>
       * @return The bytes for uploadTimestamp.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getUploadTimestampBytes() {
        return instance.getUploadTimestampBytes();
      }
      /**
       * <code>string upload_timestamp = 2;</code>
       * @param value The uploadTimestamp to set.
       * @return This builder for chaining.
       */
      public Builder setUploadTimestamp(
          java.lang.String value) {
        copyOnWrite();
        instance.setUploadTimestamp(value);
        return this;
      }
      /**
       * <code>string upload_timestamp = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearUploadTimestamp() {
        copyOnWrite();
        instance.clearUploadTimestamp();
        return this;
      }
      /**
       * <code>string upload_timestamp = 2;</code>
       * @param value The bytes for uploadTimestamp to set.
       * @return This builder for chaining.
       */
      public Builder setUploadTimestampBytes(
          com.google.protobuf.ByteString value) {
        copyOnWrite();
        instance.setUploadTimestampBytes(value);
        return this;
      }

      /**
       * <code>string api_version = 3;</code>
       * @return The apiVersion.
       */
      @java.lang.Override
      public java.lang.String getApiVersion() {
        return instance.getApiVersion();
      }
      /**
       * <code>string api_version = 3;</code>
       * @return The bytes for apiVersion.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getApiVersionBytes() {
        return instance.getApiVersionBytes();
      }
      /**
       * <code>string api_version = 3;</code>
       * @param value The apiVersion to set.
       * @return This builder for chaining.
       */
      public Builder setApiVersion(
          java.lang.String value) {
        copyOnWrite();
        instance.setApiVersion(value);
        return this;
      }
      /**
       * <code>string api_version = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearApiVersion() {
        copyOnWrite();
        instance.clearApiVersion();
        return this;
      }
      /**
       * <code>string api_version = 3;</code>
       * @param value The bytes for apiVersion to set.
       * @return This builder for chaining.
       */
      public Builder setApiVersionBytes(
          com.google.protobuf.ByteString value) {
        copyOnWrite();
        instance.setApiVersionBytes(value);
        return this;
      }

      /**
       * <pre>
       * seconds since Linux epoch
       * </pre>
       *
       * <code>double sent_at_s = 4;</code>
       * @return The sentAtS.
       */
      @java.lang.Override
      public double getSentAtS() {
        return instance.getSentAtS();
      }
      /**
       * <pre>
       * seconds since Linux epoch
       * </pre>
       *
       * <code>double sent_at_s = 4;</code>
       * @param value The sentAtS to set.
       * @return This builder for chaining.
       */
      public Builder setSentAtS(double value) {
        copyOnWrite();
        instance.setSentAtS(value);
        return this;
      }
      /**
       * <pre>
       * seconds since Linux epoch
       * </pre>
       *
       * <code>double sent_at_s = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearSentAtS() {
        copyOnWrite();
        instance.clearSentAtS();
        return this;
      }

      /**
       * <pre>
       * Microseconds since start of capture, should be associated w/ PreprocessedDataBuffer that triggered
       * the generation of this MetricsBuffer object
       * </pre>
       *
       * <code>int64 frame_timestamp = 5;</code>
       * @return The frameTimestamp.
       */
      @java.lang.Override
      public long getFrameTimestamp() {
        return instance.getFrameTimestamp();
      }
      /**
       * <pre>
       * Microseconds since start of capture, should be associated w/ PreprocessedDataBuffer that triggered
       * the generation of this MetricsBuffer object
       * </pre>
       *
       * <code>int64 frame_timestamp = 5;</code>
       * @param value The frameTimestamp to set.
       * @return This builder for chaining.
       */
      public Builder setFrameTimestamp(long value) {
        copyOnWrite();
        instance.setFrameTimestamp(value);
        return this;
      }
      /**
       * <pre>
       * Microseconds since start of capture, should be associated w/ PreprocessedDataBuffer that triggered
       * the generation of this MetricsBuffer object
       * </pre>
       *
       * <code>int64 frame_timestamp = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearFrameTimestamp() {
        copyOnWrite();
        instance.clearFrameTimestamp();
        return this;
      }

      /**
       * <pre>
       * should be set to number of actual image frames used to generate PreprocessedDataBuffer
       * that triggered the generation of this MetricsBuffer object, excluding those frames that were partially used for
       * previous buffers
       * </pre>
       *
       * <code>int32 frame_count = 6;</code>
       * @return The frameCount.
       */
      @java.lang.Override
      public int getFrameCount() {
        return instance.getFrameCount();
      }
      /**
       * <pre>
       * should be set to number of actual image frames used to generate PreprocessedDataBuffer
       * that triggered the generation of this MetricsBuffer object, excluding those frames that were partially used for
       * previous buffers
       * </pre>
       *
       * <code>int32 frame_count = 6;</code>
       * @param value The frameCount to set.
       * @return This builder for chaining.
       */
      public Builder setFrameCount(int value) {
        copyOnWrite();
        instance.setFrameCount(value);
        return this;
      }
      /**
       * <pre>
       * should be set to number of actual image frames used to generate PreprocessedDataBuffer
       * that triggered the generation of this MetricsBuffer object, excluding those frames that were partially used for
       * previous buffers
       * </pre>
       *
       * <code>int32 frame_count = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearFrameCount() {
        copyOnWrite();
        instance.clearFrameCount();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:presage.physiology.Metadata)
    }
    @java.lang.Override
    @java.lang.SuppressWarnings({"unchecked", "fallthrough"})
    protected final java.lang.Object dynamicMethod(
        com.google.protobuf.GeneratedMessageLite.MethodToInvoke method,
        java.lang.Object arg0, java.lang.Object arg1) {
      switch (method) {
        case NEW_MUTABLE_INSTANCE: {
          return new com.presage.physiology.proto.MetricsProto.Metadata();
        }
        case NEW_BUILDER: {
          return new Builder();
        }
        case BUILD_MESSAGE_INFO: {
            java.lang.Object[] objects = new java.lang.Object[] {
              "id_",
              "uploadTimestamp_",
              "apiVersion_",
              "sentAtS_",
              "frameTimestamp_",
              "frameCount_",
            };
            java.lang.String info =
                "\u0000\u0006\u0000\u0000\u0001\u0006\u0006\u0000\u0000\u0000\u0001\u0208\u0002\u0208" +
                "\u0003\u0208\u0004\u0000\u0005\u0002\u0006\u0004";
            return newMessageInfo(DEFAULT_INSTANCE, info, objects);
        }
        // fall through
        case GET_DEFAULT_INSTANCE: {
          return DEFAULT_INSTANCE;
        }
        case GET_PARSER: {
          com.google.protobuf.Parser<com.presage.physiology.proto.MetricsProto.Metadata> parser = PARSER;
          if (parser == null) {
            synchronized (com.presage.physiology.proto.MetricsProto.Metadata.class) {
              parser = PARSER;
              if (parser == null) {
                parser =
                    new DefaultInstanceBasedParser<com.presage.physiology.proto.MetricsProto.Metadata>(
                        DEFAULT_INSTANCE);
                PARSER = parser;
              }
            }
          }
          return parser;
      }
      case GET_MEMOIZED_IS_INITIALIZED: {
        return (byte) 1;
      }
      case SET_MEMOIZED_IS_INITIALIZED: {
        return null;
      }
      }
      throw new UnsupportedOperationException();
    }


    // @@protoc_insertion_point(class_scope:presage.physiology.Metadata)
    private static final com.presage.physiology.proto.MetricsProto.Metadata DEFAULT_INSTANCE;
    static {
      Metadata defaultInstance = new Metadata();
      // New instances are implicitly immutable so no need to make
      // immutable.
      DEFAULT_INSTANCE = defaultInstance;
      com.google.protobuf.GeneratedMessageLite.registerDefaultInstance(
        Metadata.class, defaultInstance);
    }

    public static com.presage.physiology.proto.MetricsProto.Metadata getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static volatile com.google.protobuf.Parser<Metadata> PARSER;

    public static com.google.protobuf.Parser<Metadata> parser() {
      return DEFAULT_INSTANCE.getParserForType();
    }
  }

  public interface MetricsBufferOrBuilder extends
      // @@protoc_insertion_point(interface_extends:presage.physiology.MetricsBuffer)
      com.google.protobuf.MessageLiteOrBuilder {

    /**
     * <code>.presage.physiology.Pulse pulse = 1;</code>
     * @return Whether the pulse field is set.
     */
    boolean hasPulse();
    /**
     * <code>.presage.physiology.Pulse pulse = 1;</code>
     * @return The pulse.
     */
    com.presage.physiology.proto.MetricsProto.Pulse getPulse();

    /**
     * <code>.presage.physiology.Breathing breathing = 2;</code>
     * @return Whether the breathing field is set.
     */
    boolean hasBreathing();
    /**
     * <code>.presage.physiology.Breathing breathing = 2;</code>
     * @return The breathing.
     */
    com.presage.physiology.proto.MetricsProto.Breathing getBreathing();

    /**
     * <code>.presage.physiology.BloodPressure blood_pressure = 3;</code>
     * @return Whether the bloodPressure field is set.
     */
    boolean hasBloodPressure();
    /**
     * <code>.presage.physiology.BloodPressure blood_pressure = 3;</code>
     * @return The bloodPressure.
     */
    com.presage.physiology.proto.MetricsProto.BloodPressure getBloodPressure();

    /**
     * <code>.presage.physiology.Face face = 4;</code>
     * @return Whether the face field is set.
     */
    boolean hasFace();
    /**
     * <code>.presage.physiology.Face face = 4;</code>
     * @return The face.
     */
    com.presage.physiology.proto.MetricsProto.Face getFace();

    /**
     * <code>.presage.physiology.Metadata metadata = 5;</code>
     * @return Whether the metadata field is set.
     */
    boolean hasMetadata();
    /**
     * <code>.presage.physiology.Metadata metadata = 5;</code>
     * @return The metadata.
     */
    com.presage.physiology.proto.MetricsProto.Metadata getMetadata();
  }
  /**
   * Protobuf type {@code presage.physiology.MetricsBuffer}
   */
  public  static final class MetricsBuffer extends
      com.google.protobuf.GeneratedMessageLite<
          MetricsBuffer, MetricsBuffer.Builder> implements
      // @@protoc_insertion_point(message_implements:presage.physiology.MetricsBuffer)
      MetricsBufferOrBuilder {
    private MetricsBuffer() {
    }
    public static final int PULSE_FIELD_NUMBER = 1;
    private com.presage.physiology.proto.MetricsProto.Pulse pulse_;
    /**
     * <code>.presage.physiology.Pulse pulse = 1;</code>
     */
    @java.lang.Override
    public boolean hasPulse() {
      return pulse_ != null;
    }
    /**
     * <code>.presage.physiology.Pulse pulse = 1;</code>
     */
    @java.lang.Override
    public com.presage.physiology.proto.MetricsProto.Pulse getPulse() {
      return pulse_ == null ? com.presage.physiology.proto.MetricsProto.Pulse.getDefaultInstance() : pulse_;
    }
    /**
     * <code>.presage.physiology.Pulse pulse = 1;</code>
     */
    private void setPulse(com.presage.physiology.proto.MetricsProto.Pulse value) {
      value.getClass();
  pulse_ = value;
      
      }
    /**
     * <code>.presage.physiology.Pulse pulse = 1;</code>
     */
    @java.lang.SuppressWarnings({"ReferenceEquality"})
    private void mergePulse(com.presage.physiology.proto.MetricsProto.Pulse value) {
      value.getClass();
  if (pulse_ != null &&
          pulse_ != com.presage.physiology.proto.MetricsProto.Pulse.getDefaultInstance()) {
        pulse_ =
          com.presage.physiology.proto.MetricsProto.Pulse.newBuilder(pulse_).mergeFrom(value).buildPartial();
      } else {
        pulse_ = value;
      }
      
    }
    /**
     * <code>.presage.physiology.Pulse pulse = 1;</code>
     */
    private void clearPulse() {  pulse_ = null;
      
    }

    public static final int BREATHING_FIELD_NUMBER = 2;
    private com.presage.physiology.proto.MetricsProto.Breathing breathing_;
    /**
     * <code>.presage.physiology.Breathing breathing = 2;</code>
     */
    @java.lang.Override
    public boolean hasBreathing() {
      return breathing_ != null;
    }
    /**
     * <code>.presage.physiology.Breathing breathing = 2;</code>
     */
    @java.lang.Override
    public com.presage.physiology.proto.MetricsProto.Breathing getBreathing() {
      return breathing_ == null ? com.presage.physiology.proto.MetricsProto.Breathing.getDefaultInstance() : breathing_;
    }
    /**
     * <code>.presage.physiology.Breathing breathing = 2;</code>
     */
    private void setBreathing(com.presage.physiology.proto.MetricsProto.Breathing value) {
      value.getClass();
  breathing_ = value;
      
      }
    /**
     * <code>.presage.physiology.Breathing breathing = 2;</code>
     */
    @java.lang.SuppressWarnings({"ReferenceEquality"})
    private void mergeBreathing(com.presage.physiology.proto.MetricsProto.Breathing value) {
      value.getClass();
  if (breathing_ != null &&
          breathing_ != com.presage.physiology.proto.MetricsProto.Breathing.getDefaultInstance()) {
        breathing_ =
          com.presage.physiology.proto.MetricsProto.Breathing.newBuilder(breathing_).mergeFrom(value).buildPartial();
      } else {
        breathing_ = value;
      }
      
    }
    /**
     * <code>.presage.physiology.Breathing breathing = 2;</code>
     */
    private void clearBreathing() {  breathing_ = null;
      
    }

    public static final int BLOOD_PRESSURE_FIELD_NUMBER = 3;
    private com.presage.physiology.proto.MetricsProto.BloodPressure bloodPressure_;
    /**
     * <code>.presage.physiology.BloodPressure blood_pressure = 3;</code>
     */
    @java.lang.Override
    public boolean hasBloodPressure() {
      return bloodPressure_ != null;
    }
    /**
     * <code>.presage.physiology.BloodPressure blood_pressure = 3;</code>
     */
    @java.lang.Override
    public com.presage.physiology.proto.MetricsProto.BloodPressure getBloodPressure() {
      return bloodPressure_ == null ? com.presage.physiology.proto.MetricsProto.BloodPressure.getDefaultInstance() : bloodPressure_;
    }
    /**
     * <code>.presage.physiology.BloodPressure blood_pressure = 3;</code>
     */
    private void setBloodPressure(com.presage.physiology.proto.MetricsProto.BloodPressure value) {
      value.getClass();
  bloodPressure_ = value;
      
      }
    /**
     * <code>.presage.physiology.BloodPressure blood_pressure = 3;</code>
     */
    @java.lang.SuppressWarnings({"ReferenceEquality"})
    private void mergeBloodPressure(com.presage.physiology.proto.MetricsProto.BloodPressure value) {
      value.getClass();
  if (bloodPressure_ != null &&
          bloodPressure_ != com.presage.physiology.proto.MetricsProto.BloodPressure.getDefaultInstance()) {
        bloodPressure_ =
          com.presage.physiology.proto.MetricsProto.BloodPressure.newBuilder(bloodPressure_).mergeFrom(value).buildPartial();
      } else {
        bloodPressure_ = value;
      }
      
    }
    /**
     * <code>.presage.physiology.BloodPressure blood_pressure = 3;</code>
     */
    private void clearBloodPressure() {  bloodPressure_ = null;
      
    }

    public static final int FACE_FIELD_NUMBER = 4;
    private com.presage.physiology.proto.MetricsProto.Face face_;
    /**
     * <code>.presage.physiology.Face face = 4;</code>
     */
    @java.lang.Override
    public boolean hasFace() {
      return face_ != null;
    }
    /**
     * <code>.presage.physiology.Face face = 4;</code>
     */
    @java.lang.Override
    public com.presage.physiology.proto.MetricsProto.Face getFace() {
      return face_ == null ? com.presage.physiology.proto.MetricsProto.Face.getDefaultInstance() : face_;
    }
    /**
     * <code>.presage.physiology.Face face = 4;</code>
     */
    private void setFace(com.presage.physiology.proto.MetricsProto.Face value) {
      value.getClass();
  face_ = value;
      
      }
    /**
     * <code>.presage.physiology.Face face = 4;</code>
     */
    @java.lang.SuppressWarnings({"ReferenceEquality"})
    private void mergeFace(com.presage.physiology.proto.MetricsProto.Face value) {
      value.getClass();
  if (face_ != null &&
          face_ != com.presage.physiology.proto.MetricsProto.Face.getDefaultInstance()) {
        face_ =
          com.presage.physiology.proto.MetricsProto.Face.newBuilder(face_).mergeFrom(value).buildPartial();
      } else {
        face_ = value;
      }
      
    }
    /**
     * <code>.presage.physiology.Face face = 4;</code>
     */
    private void clearFace() {  face_ = null;
      
    }

    public static final int METADATA_FIELD_NUMBER = 5;
    private com.presage.physiology.proto.MetricsProto.Metadata metadata_;
    /**
     * <code>.presage.physiology.Metadata metadata = 5;</code>
     */
    @java.lang.Override
    public boolean hasMetadata() {
      return metadata_ != null;
    }
    /**
     * <code>.presage.physiology.Metadata metadata = 5;</code>
     */
    @java.lang.Override
    public com.presage.physiology.proto.MetricsProto.Metadata getMetadata() {
      return metadata_ == null ? com.presage.physiology.proto.MetricsProto.Metadata.getDefaultInstance() : metadata_;
    }
    /**
     * <code>.presage.physiology.Metadata metadata = 5;</code>
     */
    private void setMetadata(com.presage.physiology.proto.MetricsProto.Metadata value) {
      value.getClass();
  metadata_ = value;
      
      }
    /**
     * <code>.presage.physiology.Metadata metadata = 5;</code>
     */
    @java.lang.SuppressWarnings({"ReferenceEquality"})
    private void mergeMetadata(com.presage.physiology.proto.MetricsProto.Metadata value) {
      value.getClass();
  if (metadata_ != null &&
          metadata_ != com.presage.physiology.proto.MetricsProto.Metadata.getDefaultInstance()) {
        metadata_ =
          com.presage.physiology.proto.MetricsProto.Metadata.newBuilder(metadata_).mergeFrom(value).buildPartial();
      } else {
        metadata_ = value;
      }
      
    }
    /**
     * <code>.presage.physiology.Metadata metadata = 5;</code>
     */
    private void clearMetadata() {  metadata_ = null;
      
    }

    public static com.presage.physiology.proto.MetricsProto.MetricsBuffer parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.MetricsProto.MetricsBuffer parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.MetricsBuffer parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.MetricsProto.MetricsBuffer parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.MetricsBuffer parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.MetricsProto.MetricsBuffer parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.MetricsBuffer parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.MetricsProto.MetricsBuffer parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.MetricsBuffer parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.MetricsProto.MetricsBuffer parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.MetricsBuffer parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.MetricsProto.MetricsBuffer parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }

    public static Builder newBuilder() {
      return (Builder) DEFAULT_INSTANCE.createBuilder();
    }
    public static Builder newBuilder(com.presage.physiology.proto.MetricsProto.MetricsBuffer prototype) {
      return (Builder) DEFAULT_INSTANCE.createBuilder(prototype);
    }

    /**
     * Protobuf type {@code presage.physiology.MetricsBuffer}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageLite.Builder<
          com.presage.physiology.proto.MetricsProto.MetricsBuffer, Builder> implements
        // @@protoc_insertion_point(builder_implements:presage.physiology.MetricsBuffer)
        com.presage.physiology.proto.MetricsProto.MetricsBufferOrBuilder {
      // Construct using com.presage.physiology.proto.MetricsProto.MetricsBuffer.newBuilder()
      private Builder() {
        super(DEFAULT_INSTANCE);
      }


      /**
       * <code>.presage.physiology.Pulse pulse = 1;</code>
       */
      @java.lang.Override
      public boolean hasPulse() {
        return instance.hasPulse();
      }
      /**
       * <code>.presage.physiology.Pulse pulse = 1;</code>
       */
      @java.lang.Override
      public com.presage.physiology.proto.MetricsProto.Pulse getPulse() {
        return instance.getPulse();
      }
      /**
       * <code>.presage.physiology.Pulse pulse = 1;</code>
       */
      public Builder setPulse(com.presage.physiology.proto.MetricsProto.Pulse value) {
        copyOnWrite();
        instance.setPulse(value);
        return this;
        }
      /**
       * <code>.presage.physiology.Pulse pulse = 1;</code>
       */
      public Builder setPulse(
          com.presage.physiology.proto.MetricsProto.Pulse.Builder builderForValue) {
        copyOnWrite();
        instance.setPulse(builderForValue.build());
        return this;
      }
      /**
       * <code>.presage.physiology.Pulse pulse = 1;</code>
       */
      public Builder mergePulse(com.presage.physiology.proto.MetricsProto.Pulse value) {
        copyOnWrite();
        instance.mergePulse(value);
        return this;
      }
      /**
       * <code>.presage.physiology.Pulse pulse = 1;</code>
       */
      public Builder clearPulse() {  copyOnWrite();
        instance.clearPulse();
        return this;
      }

      /**
       * <code>.presage.physiology.Breathing breathing = 2;</code>
       */
      @java.lang.Override
      public boolean hasBreathing() {
        return instance.hasBreathing();
      }
      /**
       * <code>.presage.physiology.Breathing breathing = 2;</code>
       */
      @java.lang.Override
      public com.presage.physiology.proto.MetricsProto.Breathing getBreathing() {
        return instance.getBreathing();
      }
      /**
       * <code>.presage.physiology.Breathing breathing = 2;</code>
       */
      public Builder setBreathing(com.presage.physiology.proto.MetricsProto.Breathing value) {
        copyOnWrite();
        instance.setBreathing(value);
        return this;
        }
      /**
       * <code>.presage.physiology.Breathing breathing = 2;</code>
       */
      public Builder setBreathing(
          com.presage.physiology.proto.MetricsProto.Breathing.Builder builderForValue) {
        copyOnWrite();
        instance.setBreathing(builderForValue.build());
        return this;
      }
      /**
       * <code>.presage.physiology.Breathing breathing = 2;</code>
       */
      public Builder mergeBreathing(com.presage.physiology.proto.MetricsProto.Breathing value) {
        copyOnWrite();
        instance.mergeBreathing(value);
        return this;
      }
      /**
       * <code>.presage.physiology.Breathing breathing = 2;</code>
       */
      public Builder clearBreathing() {  copyOnWrite();
        instance.clearBreathing();
        return this;
      }

      /**
       * <code>.presage.physiology.BloodPressure blood_pressure = 3;</code>
       */
      @java.lang.Override
      public boolean hasBloodPressure() {
        return instance.hasBloodPressure();
      }
      /**
       * <code>.presage.physiology.BloodPressure blood_pressure = 3;</code>
       */
      @java.lang.Override
      public com.presage.physiology.proto.MetricsProto.BloodPressure getBloodPressure() {
        return instance.getBloodPressure();
      }
      /**
       * <code>.presage.physiology.BloodPressure blood_pressure = 3;</code>
       */
      public Builder setBloodPressure(com.presage.physiology.proto.MetricsProto.BloodPressure value) {
        copyOnWrite();
        instance.setBloodPressure(value);
        return this;
        }
      /**
       * <code>.presage.physiology.BloodPressure blood_pressure = 3;</code>
       */
      public Builder setBloodPressure(
          com.presage.physiology.proto.MetricsProto.BloodPressure.Builder builderForValue) {
        copyOnWrite();
        instance.setBloodPressure(builderForValue.build());
        return this;
      }
      /**
       * <code>.presage.physiology.BloodPressure blood_pressure = 3;</code>
       */
      public Builder mergeBloodPressure(com.presage.physiology.proto.MetricsProto.BloodPressure value) {
        copyOnWrite();
        instance.mergeBloodPressure(value);
        return this;
      }
      /**
       * <code>.presage.physiology.BloodPressure blood_pressure = 3;</code>
       */
      public Builder clearBloodPressure() {  copyOnWrite();
        instance.clearBloodPressure();
        return this;
      }

      /**
       * <code>.presage.physiology.Face face = 4;</code>
       */
      @java.lang.Override
      public boolean hasFace() {
        return instance.hasFace();
      }
      /**
       * <code>.presage.physiology.Face face = 4;</code>
       */
      @java.lang.Override
      public com.presage.physiology.proto.MetricsProto.Face getFace() {
        return instance.getFace();
      }
      /**
       * <code>.presage.physiology.Face face = 4;</code>
       */
      public Builder setFace(com.presage.physiology.proto.MetricsProto.Face value) {
        copyOnWrite();
        instance.setFace(value);
        return this;
        }
      /**
       * <code>.presage.physiology.Face face = 4;</code>
       */
      public Builder setFace(
          com.presage.physiology.proto.MetricsProto.Face.Builder builderForValue) {
        copyOnWrite();
        instance.setFace(builderForValue.build());
        return this;
      }
      /**
       * <code>.presage.physiology.Face face = 4;</code>
       */
      public Builder mergeFace(com.presage.physiology.proto.MetricsProto.Face value) {
        copyOnWrite();
        instance.mergeFace(value);
        return this;
      }
      /**
       * <code>.presage.physiology.Face face = 4;</code>
       */
      public Builder clearFace() {  copyOnWrite();
        instance.clearFace();
        return this;
      }

      /**
       * <code>.presage.physiology.Metadata metadata = 5;</code>
       */
      @java.lang.Override
      public boolean hasMetadata() {
        return instance.hasMetadata();
      }
      /**
       * <code>.presage.physiology.Metadata metadata = 5;</code>
       */
      @java.lang.Override
      public com.presage.physiology.proto.MetricsProto.Metadata getMetadata() {
        return instance.getMetadata();
      }
      /**
       * <code>.presage.physiology.Metadata metadata = 5;</code>
       */
      public Builder setMetadata(com.presage.physiology.proto.MetricsProto.Metadata value) {
        copyOnWrite();
        instance.setMetadata(value);
        return this;
        }
      /**
       * <code>.presage.physiology.Metadata metadata = 5;</code>
       */
      public Builder setMetadata(
          com.presage.physiology.proto.MetricsProto.Metadata.Builder builderForValue) {
        copyOnWrite();
        instance.setMetadata(builderForValue.build());
        return this;
      }
      /**
       * <code>.presage.physiology.Metadata metadata = 5;</code>
       */
      public Builder mergeMetadata(com.presage.physiology.proto.MetricsProto.Metadata value) {
        copyOnWrite();
        instance.mergeMetadata(value);
        return this;
      }
      /**
       * <code>.presage.physiology.Metadata metadata = 5;</code>
       */
      public Builder clearMetadata() {  copyOnWrite();
        instance.clearMetadata();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:presage.physiology.MetricsBuffer)
    }
    @java.lang.Override
    @java.lang.SuppressWarnings({"unchecked", "fallthrough"})
    protected final java.lang.Object dynamicMethod(
        com.google.protobuf.GeneratedMessageLite.MethodToInvoke method,
        java.lang.Object arg0, java.lang.Object arg1) {
      switch (method) {
        case NEW_MUTABLE_INSTANCE: {
          return new com.presage.physiology.proto.MetricsProto.MetricsBuffer();
        }
        case NEW_BUILDER: {
          return new Builder();
        }
        case BUILD_MESSAGE_INFO: {
            java.lang.Object[] objects = new java.lang.Object[] {
              "pulse_",
              "breathing_",
              "bloodPressure_",
              "face_",
              "metadata_",
            };
            java.lang.String info =
                "\u0000\u0005\u0000\u0000\u0001\u0005\u0005\u0000\u0000\u0000\u0001\t\u0002\t\u0003" +
                "\t\u0004\t\u0005\t";
            return newMessageInfo(DEFAULT_INSTANCE, info, objects);
        }
        // fall through
        case GET_DEFAULT_INSTANCE: {
          return DEFAULT_INSTANCE;
        }
        case GET_PARSER: {
          com.google.protobuf.Parser<com.presage.physiology.proto.MetricsProto.MetricsBuffer> parser = PARSER;
          if (parser == null) {
            synchronized (com.presage.physiology.proto.MetricsProto.MetricsBuffer.class) {
              parser = PARSER;
              if (parser == null) {
                parser =
                    new DefaultInstanceBasedParser<com.presage.physiology.proto.MetricsProto.MetricsBuffer>(
                        DEFAULT_INSTANCE);
                PARSER = parser;
              }
            }
          }
          return parser;
      }
      case GET_MEMOIZED_IS_INITIALIZED: {
        return (byte) 1;
      }
      case SET_MEMOIZED_IS_INITIALIZED: {
        return null;
      }
      }
      throw new UnsupportedOperationException();
    }


    // @@protoc_insertion_point(class_scope:presage.physiology.MetricsBuffer)
    private static final com.presage.physiology.proto.MetricsProto.MetricsBuffer DEFAULT_INSTANCE;
    static {
      MetricsBuffer defaultInstance = new MetricsBuffer();
      // New instances are implicitly immutable so no need to make
      // immutable.
      DEFAULT_INSTANCE = defaultInstance;
      com.google.protobuf.GeneratedMessageLite.registerDefaultInstance(
        MetricsBuffer.class, defaultInstance);
    }

    public static com.presage.physiology.proto.MetricsProto.MetricsBuffer getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static volatile com.google.protobuf.Parser<MetricsBuffer> PARSER;

    public static com.google.protobuf.Parser<MetricsBuffer> parser() {
      return DEFAULT_INSTANCE.getParserForType();
    }
  }

  public interface MetricsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:presage.physiology.Metrics)
      com.google.protobuf.MessageLiteOrBuilder {

    /**
     * <code>.presage.physiology.Breathing breathing = 1;</code>
     * @return Whether the breathing field is set.
     */
    boolean hasBreathing();
    /**
     * <code>.presage.physiology.Breathing breathing = 1;</code>
     * @return The breathing.
     */
    com.presage.physiology.proto.MetricsProto.Breathing getBreathing();
  }
  /**
   * Protobuf type {@code presage.physiology.Metrics}
   */
  public  static final class Metrics extends
      com.google.protobuf.GeneratedMessageLite<
          Metrics, Metrics.Builder> implements
      // @@protoc_insertion_point(message_implements:presage.physiology.Metrics)
      MetricsOrBuilder {
    private Metrics() {
    }
    public static final int BREATHING_FIELD_NUMBER = 1;
    private com.presage.physiology.proto.MetricsProto.Breathing breathing_;
    /**
     * <code>.presage.physiology.Breathing breathing = 1;</code>
     */
    @java.lang.Override
    public boolean hasBreathing() {
      return breathing_ != null;
    }
    /**
     * <code>.presage.physiology.Breathing breathing = 1;</code>
     */
    @java.lang.Override
    public com.presage.physiology.proto.MetricsProto.Breathing getBreathing() {
      return breathing_ == null ? com.presage.physiology.proto.MetricsProto.Breathing.getDefaultInstance() : breathing_;
    }
    /**
     * <code>.presage.physiology.Breathing breathing = 1;</code>
     */
    private void setBreathing(com.presage.physiology.proto.MetricsProto.Breathing value) {
      value.getClass();
  breathing_ = value;
      
      }
    /**
     * <code>.presage.physiology.Breathing breathing = 1;</code>
     */
    @java.lang.SuppressWarnings({"ReferenceEquality"})
    private void mergeBreathing(com.presage.physiology.proto.MetricsProto.Breathing value) {
      value.getClass();
  if (breathing_ != null &&
          breathing_ != com.presage.physiology.proto.MetricsProto.Breathing.getDefaultInstance()) {
        breathing_ =
          com.presage.physiology.proto.MetricsProto.Breathing.newBuilder(breathing_).mergeFrom(value).buildPartial();
      } else {
        breathing_ = value;
      }
      
    }
    /**
     * <code>.presage.physiology.Breathing breathing = 1;</code>
     */
    private void clearBreathing() {  breathing_ = null;
      
    }

    public static com.presage.physiology.proto.MetricsProto.Metrics parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.MetricsProto.Metrics parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.Metrics parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.MetricsProto.Metrics parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.Metrics parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static com.presage.physiology.proto.MetricsProto.Metrics parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.Metrics parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.MetricsProto.Metrics parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.Metrics parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.MetricsProto.Metrics parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static com.presage.physiology.proto.MetricsProto.Metrics parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static com.presage.physiology.proto.MetricsProto.Metrics parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }

    public static Builder newBuilder() {
      return (Builder) DEFAULT_INSTANCE.createBuilder();
    }
    public static Builder newBuilder(com.presage.physiology.proto.MetricsProto.Metrics prototype) {
      return (Builder) DEFAULT_INSTANCE.createBuilder(prototype);
    }

    /**
     * Protobuf type {@code presage.physiology.Metrics}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageLite.Builder<
          com.presage.physiology.proto.MetricsProto.Metrics, Builder> implements
        // @@protoc_insertion_point(builder_implements:presage.physiology.Metrics)
        com.presage.physiology.proto.MetricsProto.MetricsOrBuilder {
      // Construct using com.presage.physiology.proto.MetricsProto.Metrics.newBuilder()
      private Builder() {
        super(DEFAULT_INSTANCE);
      }


      /**
       * <code>.presage.physiology.Breathing breathing = 1;</code>
       */
      @java.lang.Override
      public boolean hasBreathing() {
        return instance.hasBreathing();
      }
      /**
       * <code>.presage.physiology.Breathing breathing = 1;</code>
       */
      @java.lang.Override
      public com.presage.physiology.proto.MetricsProto.Breathing getBreathing() {
        return instance.getBreathing();
      }
      /**
       * <code>.presage.physiology.Breathing breathing = 1;</code>
       */
      public Builder setBreathing(com.presage.physiology.proto.MetricsProto.Breathing value) {
        copyOnWrite();
        instance.setBreathing(value);
        return this;
        }
      /**
       * <code>.presage.physiology.Breathing breathing = 1;</code>
       */
      public Builder setBreathing(
          com.presage.physiology.proto.MetricsProto.Breathing.Builder builderForValue) {
        copyOnWrite();
        instance.setBreathing(builderForValue.build());
        return this;
      }
      /**
       * <code>.presage.physiology.Breathing breathing = 1;</code>
       */
      public Builder mergeBreathing(com.presage.physiology.proto.MetricsProto.Breathing value) {
        copyOnWrite();
        instance.mergeBreathing(value);
        return this;
      }
      /**
       * <code>.presage.physiology.Breathing breathing = 1;</code>
       */
      public Builder clearBreathing() {  copyOnWrite();
        instance.clearBreathing();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:presage.physiology.Metrics)
    }
    @java.lang.Override
    @java.lang.SuppressWarnings({"unchecked", "fallthrough"})
    protected final java.lang.Object dynamicMethod(
        com.google.protobuf.GeneratedMessageLite.MethodToInvoke method,
        java.lang.Object arg0, java.lang.Object arg1) {
      switch (method) {
        case NEW_MUTABLE_INSTANCE: {
          return new com.presage.physiology.proto.MetricsProto.Metrics();
        }
        case NEW_BUILDER: {
          return new Builder();
        }
        case BUILD_MESSAGE_INFO: {
            java.lang.Object[] objects = new java.lang.Object[] {
              "breathing_",
            };
            java.lang.String info =
                "\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0000\u0000\u0001\t";
            return newMessageInfo(DEFAULT_INSTANCE, info, objects);
        }
        // fall through
        case GET_DEFAULT_INSTANCE: {
          return DEFAULT_INSTANCE;
        }
        case GET_PARSER: {
          com.google.protobuf.Parser<com.presage.physiology.proto.MetricsProto.Metrics> parser = PARSER;
          if (parser == null) {
            synchronized (com.presage.physiology.proto.MetricsProto.Metrics.class) {
              parser = PARSER;
              if (parser == null) {
                parser =
                    new DefaultInstanceBasedParser<com.presage.physiology.proto.MetricsProto.Metrics>(
                        DEFAULT_INSTANCE);
                PARSER = parser;
              }
            }
          }
          return parser;
      }
      case GET_MEMOIZED_IS_INITIALIZED: {
        return (byte) 1;
      }
      case SET_MEMOIZED_IS_INITIALIZED: {
        return null;
      }
      }
      throw new UnsupportedOperationException();
    }


    // @@protoc_insertion_point(class_scope:presage.physiology.Metrics)
    private static final com.presage.physiology.proto.MetricsProto.Metrics DEFAULT_INSTANCE;
    static {
      Metrics defaultInstance = new Metrics();
      // New instances are implicitly immutable so no need to make
      // immutable.
      DEFAULT_INSTANCE = defaultInstance;
      com.google.protobuf.GeneratedMessageLite.registerDefaultInstance(
        Metrics.class, defaultInstance);
    }

    public static com.presage.physiology.proto.MetricsProto.Metrics getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static volatile com.google.protobuf.Parser<Metrics> PARSER;

    public static com.google.protobuf.Parser<Metrics> parser() {
      return DEFAULT_INSTANCE.getParserForType();
    }
  }


  static {
  }

  // @@protoc_insertion_point(outer_class_scope)
}
