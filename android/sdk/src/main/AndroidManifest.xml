<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-feature android:name="android.hardware.camera" android:required="true" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.INTERNET" />

    <application
        android:enableOnBackInvokedCallback="true">
        <activity
            android:name=".ui.OnboardingTutorialActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Transparent" />
        <activity
            android:name=".ui.SmartSpectraActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Material3.Light.NoActionBar" />
        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="${applicationId}.androidx-startup"
            android:exported="false"
            tools:node="merge">
            <!-- This entry makes SmartSpectraInitializer discoverable. -->
            <meta-data  android:name="com.presagetech.smartspectra.SmartSpectraInitializer"
                android:value="androidx.startup" />
        </provider>
    </application>
</manifest>
