<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:gravity="center"
    android:orientation="vertical">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/img_upload"
        android:layout_width="128dp"
        android:layout_height="128dp"
        android:src="@drawable/ic_baseline_upload_24" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/text_status"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="32dp"
        android:layout_marginBottom="32dp"
        android:gravity="center"
        android:lineSpacingMultiplier="1.3"
        android:maxWidth="300dp"
        android:text="@string/uploading_captured_data"
        android:textColor="@color/black"
        android:textSize="21sp" />

    <ProgressBar
        android:id="@+id/progress_bar"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="32dp"
        android:indeterminateTint="@color/red"
        android:max="100"
        android:progressTint="@color/red"
        tools:indeterminate="true" />

</LinearLayout>
