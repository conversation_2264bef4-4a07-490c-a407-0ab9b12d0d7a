<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/smart_spectra_button_background"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    tools:layout_height="56dp"
    tools:parentTag="android.widget.LinearLayout">

    <LinearLayout
        android:id="@+id/button_checkup"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingHorizontal="16dp"
            android:src="@drawable/ic_baseline_favorite_24" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/checkup"
            android:textColor="@color/white"
            android:textSize="18sp" />
    </LinearLayout>

    <FrameLayout
        android:id="@+id/button_info"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:background="@drawable/smart_spectra_info_button_background"
        android:paddingHorizontal="16dp">

        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:src="@drawable/ic_info_24" />
    </FrameLayout>

</merge>
