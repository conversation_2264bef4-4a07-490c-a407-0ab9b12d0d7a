<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white"
    android:forceDarkAllowed="false">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="0dp"
        android:background="@color/white"
        android:layout_height="?attr/actionBarSize"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:elevation="4dp"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.camera.view.PreviewView
        android:id="@+id/preview_view"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        app:layout_constraintDimensionRatio="720:1280"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar" />

    <View
        android:id="@+id/view_frame_start"
        android:layout_width="@dimen/camera_preview_frame_size"
        android:layout_height="0dp"
        android:background="@color/camera_preview_frame"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/view_frame_end"
        android:layout_width="@dimen/camera_preview_frame_size"
        android:layout_height="0dp"
        android:background="@color/camera_preview_frame"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/button_mode_toggle"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginTop="10dp"
        android:gravity="center"
        android:backgroundTint="@color/screening_button_background_tint"
        android:contentDescription="Toggle SmartSpectra Mode"
        android:padding="8dp"
        app:icon="@drawable/ic_line_chart"
        app:iconTint="@color/screening_button_icon_tint"
        app:iconSize="32dp"
        app:iconPadding="0dp"
        app:layout_constraintStart_toEndOf="@id/view_frame_start"
        app:layout_constraintTop_toBottomOf="@+id/info_button"
        app:layout_constraintVertical_bias="0.5" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/button_flip_camera"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginTop="10dp"
        android:gravity="center"
        android:backgroundTint="@color/screening_button_background_tint"
        android:contentDescription="Flip camera Button"
        android:padding="8dp"
        app:icon="@drawable/ic_flip_camera"
        app:iconTint="@color/screening_button_icon_tint"
        app:iconSize="32dp"
        app:iconPadding="0dp"
        app:layout_constraintEnd_toStartOf="@id/view_frame_end"
        app:layout_constraintTop_toBottomOf="@+id/info_button"
        app:layout_constraintVertical_bias="0.5" />

    <com.presagetech.smartspectra.ui.screening.ScreeningPlotView
        android:id="@+id/screeningPlotView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:paddingHorizontal="10dp"
        app:layout_constraintTop_toBottomOf="@+id/button_mode_toggle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <View
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/camera_preview_frame_size_neg"
        android:background="@color/camera_preview_frame"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/view_frame_end"
        app:layout_constraintStart_toEndOf="@id/view_frame_start"
        app:layout_constraintTop_toTopOf="@id/text_hint" />

    <ImageButton
        android:id="@+id/info_button"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="8dp"
        android:background="@android:color/transparent"
        android:contentDescription="@string/info_button_description"
        android:src="@drawable/ic_info_24"
        android:padding="0dp"
        android:elevation="8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:id="@+id/button_recording"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_marginBottom="40dp"
        android:background="@drawable/record_background"
        android:gravity="center"
        android:text="@string/record"
        android:textColor="@color/white"
        android:textSize="18sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/text_hint"
        android:layout_width="330dp"
        android:layout_height="wrap_content"
        android:layout_margin="32dp"
        android:background="@drawable/alert_background"
        android:gravity="center"
        android:padding="6dp"
        android:textColor="@color/white"
        android:textSize="22sp"
        app:layout_constraintBottom_toTopOf="@+id/button_recording"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:text="This is hint text" />

    <TextView
        android:id="@+id/text_timer"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="60dp"
        android:background="@drawable/timer_background"
        android:gravity="center"
        android:includeFontPadding="false"
        android:minWidth="60dp"
        android:minHeight="60dp"
        android:textColor="@color/white"
        android:textSize="30sp"
        app:layout_constraintBottom_toBottomOf="@id/button_recording"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintStart_toStartOf="@id/text_hint"
        app:layout_constraintTop_toTopOf="@id/button_recording"
        tools:text="15" />

    <TextView
        android:id="@+id/fps_text_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text=""
        android:textColor="#FF0000"
        android:textSize="18sp"
        android:padding="4dp"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:layout_margin="16dp"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@id/button_recording"
        app:layout_constraintEnd_toEndOf="@id/text_hint"/>

</androidx.constraintlayout.widget.ConstraintLayout>
