<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">


    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/show_tutorial"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:gravity="center"
        android:text="@string/show_tutorial"
        android:textColor="@color/black" />

    <View
        android:layout_width="wrap_content"
        android:layout_height="1dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:background="@color/gray" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/txt_instruction_of_use"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:gravity="center"
        android:text="@string/instruction_of_use"
        android:textColor="@color/black" />

    <View
        android:layout_width="wrap_content"
        android:layout_height="1dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:background="@color/gray" />


    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/txt_terms_of_service"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:gravity="center"
        android:text="@string/terms_of_services"
        android:textColor="@color/black" />

    <View
        android:layout_width="wrap_content"
        android:layout_height="1dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:background="@color/gray" />


    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/txt_privacy_policy"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:gravity="center"
        android:text="@string/privacy_policy"
        android:textColor="@color/black" />

    <View
        android:layout_width="wrap_content"
        android:layout_height="1dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:background="@color/gray" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/txt_contact_us"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:gravity="center"
        android:text="@string/contact_us"
        android:textColor="@color/black" />

    <View
        android:layout_width="wrap_content"
        android:layout_height="1dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:background="@color/gray" />


</LinearLayout>