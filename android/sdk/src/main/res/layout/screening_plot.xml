<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/pulseRateTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:minWidth="125dp"
        android:text="Pulse Rate"
        android:textColor="@color/black"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textAlignment="viewStart"
        android:shadowColor="@color/white"
        android:shadowRadius="8"
        app:layout_constraintTop_toTopOf="@+id/pulsePlot"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/pulsePlot"
        app:layout_constraintBottom_toTopOf="@+id/pulseRateValue" />

    <TextView
        android:id="@+id/pulseRateValue"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="--"
        android:textColor="@color/black"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textAlignment="viewStart"
        android:shadowColor="@color/white"
        android:shadowRadius="8"
        app:layout_constraintTop_toBottomOf="@+id/pulseRateTitle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/pulsePlot"
        app:layout_constraintBottom_toBottomOf="@+id/pulsePlot" />

    <TextView
        android:id="@+id/breathingRateTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:minWidth="125dp"
        android:text="Breathing Rate"
        android:textColor="@color/black"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textAlignment="viewStart"
        android:shadowColor="@color/white"
        android:shadowRadius="8"
        app:layout_constraintTop_toTopOf="@+id/breathingPlot"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/breathingPlot"
        app:layout_constraintBottom_toTopOf="@+id/breathingRateValue" />

    <TextView
        android:id="@+id/breathingRateValue"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="--"
        android:textStyle="bold"
        android:textSize="18sp"
        android:textColor="@color/black"
        android:textAlignment="viewStart"
        android:shadowColor="@color/white"
        android:shadowRadius="8"
        app:layout_constraintTop_toBottomOf="@+id/breathingRateTitle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/breathingPlot"
        app:layout_constraintBottom_toBottomOf="@+id/breathingPlot"/>
    <com.github.mikephil.charting.charts.LineChart
        android:id="@+id/pulsePlot"
        android:layout_width="0dp"
        android:layout_height="125dp"
        android:layout_marginStart="10dp"
        android:shadowColor="@color/white"
        android:shadowRadius="8"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toEndOf="@+id/pulseRateTitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/breathingPlot" />

    <com.github.mikephil.charting.charts.LineChart
        android:id="@+id/breathingPlot"
        android:layout_width="0dp"
        android:layout_height="125dp"
        android:layout_marginStart="10dp"
        android:shadowColor="@color/white"
        android:shadowRadius="8"
        app:layout_constraintTop_toBottomOf="@+id/pulsePlot"
        app:layout_constraintStart_toEndOf="@+id/breathingRateTitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>