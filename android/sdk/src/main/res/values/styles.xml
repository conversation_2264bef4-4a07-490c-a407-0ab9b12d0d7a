<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="Theme.Transparent" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:backgroundDimEnabled">false</item>
    </style>
    <style name="FullScreenDialogTheme" parent="Theme.AppCompat.Light.Dialog">
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowCloseOnTouchOutside">false</item>
        <item name="android:windowBackground">@drawable/dialog_background</item>
    </style>
</resources>