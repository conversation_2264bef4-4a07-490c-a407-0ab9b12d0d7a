name: "💡 Feature Request"
description: Create a new ticket for a new feature request
title: "💡 [REQUEST] - <title>"
labels: [
  "question"
]
body:
  - type: input
    id: start_date
    attributes:
      label: "Start Date"
      description: Start of development
      placeholder: "month/day/year"
    validations:
      required: false
  - type: textarea
    id: implementation_pr
    attributes:
      label: "Implementation PR"
      description: Pull request used
      placeholder: "#Pull Request ID"
    validations:
      required: false
  - type: textarea
    id: reference_issues
    attributes:
      label: "Reference Issues"
      description: Common issues
      placeholder: "#Issues IDs"
    validations:
      required: false
  - type: textarea
    id: summary
    attributes:
      label: "Summary"
      description: Provide a brief explanation of the feature
      placeholder: Describe in a few lines your feature request
    validations:
      required: true
  - type: textarea
    id: basic_example
    attributes:
      label: "Example Use Cases"
      description: Indicate here some basic example use cases of your feature.
      placeholder: A few specific words about your feature request.
    validations:
      required: true
  - type: textarea
    id: drawbacks
    attributes:
      label: "Drawbacks"
      description: Staying as objective as possible, what potential drawbacks or negative impacts do you foresee if the requested feature was implemented?
      placeholder: Potential Drawbacks
    validations:
      required: true
  - type: textarea
    id: unresolved_question
    attributes:
      label: "Unresolved questions"
      description: What questions still remain unresolved ?
      placeholder: Identify any unresolved issues.
    validations:
      required: false