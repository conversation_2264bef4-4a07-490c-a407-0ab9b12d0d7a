// DO NOT EDIT.
// swift-format-ignore-file
//
// Generated by the Swift generator plugin for the protocol buffer compiler.
// Source: modules/messages/metrics.proto
//
// For information on using the generated types, please see the documentation:
//   https://github.com/apple/swift-protobuf/

import Foundation
import SwiftProtobuf

// If the compiler emits an error on this type, it is because this file
// was generated by a version of the `protoc` Swift plug-in that is
// incompatible with the version of SwiftProtobuf to which you are linking.
// Please ensure that you are building against the same version of the API
// that was used to generate this file.
fileprivate struct _GeneratedWithProtocGenSwiftVersion: SwiftProtobuf.ProtobufAPIVersionCheck {
  struct _2: SwiftProtobuf.ProtobufAPIVersion_2 {}
  typealias Version = _2
}

public struct Presage_Physiology_Measurement {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  public var time: Float = 0

  public var value: Float = 0

  public var stable: Bool = false

  public var unknownFields = SwiftProtobuf.UnknownStorage()

  public init() {}
}

public struct Presage_Physiology_DetectionStatus {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  public var time: Float = 0

  public var detected: Bool = false

  public var stable: Bool = false

  public var unknownFields = SwiftProtobuf.UnknownStorage()

  public init() {}
}

public struct Presage_Physiology_MeasurementWithConfidence {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  public var time: Float = 0

  public var value: Float = 0

  public var stable: Bool = false

  public var confidence: Float = 0

  public var unknownFields = SwiftProtobuf.UnknownStorage()

  public init() {}
}

public struct Presage_Physiology_Strict {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  public var value: Float = 0

  public var unknownFields = SwiftProtobuf.UnknownStorage()

  public init() {}
}

public struct Presage_Physiology_Pulse {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  public var rate: [Presage_Physiology_MeasurementWithConfidence] = []

  public var trace: [Presage_Physiology_Measurement] = []

  public var pulseRespirationQuotient: [Presage_Physiology_Measurement] = []

  public var strict: Presage_Physiology_Strict {
    get {return _strict ?? Presage_Physiology_Strict()}
    set {_strict = newValue}
  }
  /// Returns true if `strict` has been explicitly set.
  public var hasStrict: Bool {return self._strict != nil}
  /// Clears the value of `strict`. Subsequent reads from it will return its default value.
  public mutating func clearStrict() {self._strict = nil}

  public var unknownFields = SwiftProtobuf.UnknownStorage()

  public init() {}

  fileprivate var _strict: Presage_Physiology_Strict? = nil
}

public struct Presage_Physiology_Trace {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  public var sample: [Presage_Physiology_Measurement] = []

  public var unknownFields = SwiftProtobuf.UnknownStorage()

  public init() {}
}

public struct Presage_Physiology_Breathing {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  public var rate: [Presage_Physiology_MeasurementWithConfidence] = []

  public var upperTrace: [Presage_Physiology_Measurement] = []

  public var lowerTrace: [Presage_Physiology_Measurement] = []

  public var amplitude: [Presage_Physiology_Measurement] = []

  public var apnea: [Presage_Physiology_DetectionStatus] = []

  public var respiratoryLineLength: [Presage_Physiology_Measurement] = []

  public var baseline: [Presage_Physiology_Measurement] = []

  public var inhaleExhaleRatio: [Presage_Physiology_Measurement] = []

  public var strict: Presage_Physiology_Strict {
    get {return _strict ?? Presage_Physiology_Strict()}
    set {_strict = newValue}
  }
  /// Returns true if `strict` has been explicitly set.
  public var hasStrict: Bool {return self._strict != nil}
  /// Clears the value of `strict`. Subsequent reads from it will return its default value.
  public mutating func clearStrict() {self._strict = nil}

  public var unknownFields = SwiftProtobuf.UnknownStorage()

  public init() {}

  fileprivate var _strict: Presage_Physiology_Strict? = nil
}

public struct Presage_Physiology_BloodPressure {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  public var phasic: [Presage_Physiology_MeasurementWithConfidence] = []

  public var unknownFields = SwiftProtobuf.UnknownStorage()

  public init() {}
}

public struct Presage_Physiology_Landmarks {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  public var time: Float = 0

  public var value: [Presage_Physiology_Point2dFloat] = []

  public var stable: Bool = false

  public var unknownFields = SwiftProtobuf.UnknownStorage()

  public init() {}
}

public struct Presage_Physiology_Face {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  public var blinking: [Presage_Physiology_DetectionStatus] = []

  public var talking: [Presage_Physiology_DetectionStatus] = []

  public var landmarks: [Presage_Physiology_Landmarks] = []

  public var unknownFields = SwiftProtobuf.UnknownStorage()

  public init() {}
}

public struct Presage_Physiology_Metadata {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  public var id: String = String()

  public var uploadTimestamp: String = String()

  public var apiVersion: String = String()

  public var sentAtS: Double = 0

  public var frameTimestamp: Int64 = 0

  public var frameCount: Int32 = 0

  public var unknownFields = SwiftProtobuf.UnknownStorage()

  public init() {}
}

public struct Presage_Physiology_MetricsBuffer {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  public var pulse: Presage_Physiology_Pulse {
    get {return _storage._pulse ?? Presage_Physiology_Pulse()}
    set {_uniqueStorage()._pulse = newValue}
  }
  /// Returns true if `pulse` has been explicitly set.
  public var hasPulse: Bool {return _storage._pulse != nil}
  /// Clears the value of `pulse`. Subsequent reads from it will return its default value.
  public mutating func clearPulse() {_uniqueStorage()._pulse = nil}

  public var breathing: Presage_Physiology_Breathing {
    get {return _storage._breathing ?? Presage_Physiology_Breathing()}
    set {_uniqueStorage()._breathing = newValue}
  }
  /// Returns true if `breathing` has been explicitly set.
  public var hasBreathing: Bool {return _storage._breathing != nil}
  /// Clears the value of `breathing`. Subsequent reads from it will return its default value.
  public mutating func clearBreathing() {_uniqueStorage()._breathing = nil}

  public var bloodPressure: Presage_Physiology_BloodPressure {
    get {return _storage._bloodPressure ?? Presage_Physiology_BloodPressure()}
    set {_uniqueStorage()._bloodPressure = newValue}
  }
  /// Returns true if `bloodPressure` has been explicitly set.
  public var hasBloodPressure: Bool {return _storage._bloodPressure != nil}
  /// Clears the value of `bloodPressure`. Subsequent reads from it will return its default value.
  public mutating func clearBloodPressure() {_uniqueStorage()._bloodPressure = nil}

  public var face: Presage_Physiology_Face {
    get {return _storage._face ?? Presage_Physiology_Face()}
    set {_uniqueStorage()._face = newValue}
  }
  /// Returns true if `face` has been explicitly set.
  public var hasFace: Bool {return _storage._face != nil}
  /// Clears the value of `face`. Subsequent reads from it will return its default value.
  public mutating func clearFace() {_uniqueStorage()._face = nil}

  public var metadata: Presage_Physiology_Metadata {
    get {return _storage._metadata ?? Presage_Physiology_Metadata()}
    set {_uniqueStorage()._metadata = newValue}
  }
  /// Returns true if `metadata` has been explicitly set.
  public var hasMetadata: Bool {return _storage._metadata != nil}
  /// Clears the value of `metadata`. Subsequent reads from it will return its default value.
  public mutating func clearMetadata() {_uniqueStorage()._metadata = nil}

  public var unknownFields = SwiftProtobuf.UnknownStorage()

  public init() {}

  fileprivate var _storage = _StorageClass.defaultInstance
}

public struct Presage_Physiology_Metrics {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  public var breathing: Presage_Physiology_Breathing {
    get {return _breathing ?? Presage_Physiology_Breathing()}
    set {_breathing = newValue}
  }
  /// Returns true if `breathing` has been explicitly set.
  public var hasBreathing: Bool {return self._breathing != nil}
  /// Clears the value of `breathing`. Subsequent reads from it will return its default value.
  public mutating func clearBreathing() {self._breathing = nil}

  public var unknownFields = SwiftProtobuf.UnknownStorage()

  public init() {}

  fileprivate var _breathing: Presage_Physiology_Breathing? = nil
}

#if swift(>=5.5) && canImport(_Concurrency)
extension Presage_Physiology_Measurement: @unchecked Sendable {}
extension Presage_Physiology_DetectionStatus: @unchecked Sendable {}
extension Presage_Physiology_MeasurementWithConfidence: @unchecked Sendable {}
extension Presage_Physiology_Strict: @unchecked Sendable {}
extension Presage_Physiology_Pulse: @unchecked Sendable {}
extension Presage_Physiology_Trace: @unchecked Sendable {}
extension Presage_Physiology_Breathing: @unchecked Sendable {}
extension Presage_Physiology_BloodPressure: @unchecked Sendable {}
extension Presage_Physiology_Landmarks: @unchecked Sendable {}
extension Presage_Physiology_Face: @unchecked Sendable {}
extension Presage_Physiology_Metadata: @unchecked Sendable {}
extension Presage_Physiology_MetricsBuffer: @unchecked Sendable {}
extension Presage_Physiology_Metrics: @unchecked Sendable {}
#endif  // swift(>=5.5) && canImport(_Concurrency)

// MARK: - Code below here is support for the SwiftProtobuf runtime.

fileprivate let _protobuf_package = "presage.physiology"

extension Presage_Physiology_Measurement: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  public static let protoMessageName: String = _protobuf_package + ".Measurement"
  public static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "time"),
    2: .same(proto: "value"),
    3: .same(proto: "stable"),
  ]

  public mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularFloatField(value: &self.time) }()
      case 2: try { try decoder.decodeSingularFloatField(value: &self.value) }()
      case 3: try { try decoder.decodeSingularBoolField(value: &self.stable) }()
      default: break
      }
    }
  }

  public func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if self.time != 0 {
      try visitor.visitSingularFloatField(value: self.time, fieldNumber: 1)
    }
    if self.value != 0 {
      try visitor.visitSingularFloatField(value: self.value, fieldNumber: 2)
    }
    if self.stable != false {
      try visitor.visitSingularBoolField(value: self.stable, fieldNumber: 3)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Presage_Physiology_Measurement, rhs: Presage_Physiology_Measurement) -> Bool {
    if lhs.time != rhs.time {return false}
    if lhs.value != rhs.value {return false}
    if lhs.stable != rhs.stable {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Presage_Physiology_DetectionStatus: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  public static let protoMessageName: String = _protobuf_package + ".DetectionStatus"
  public static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "time"),
    2: .same(proto: "detected"),
    3: .same(proto: "stable"),
  ]

  public mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularFloatField(value: &self.time) }()
      case 2: try { try decoder.decodeSingularBoolField(value: &self.detected) }()
      case 3: try { try decoder.decodeSingularBoolField(value: &self.stable) }()
      default: break
      }
    }
  }

  public func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if self.time != 0 {
      try visitor.visitSingularFloatField(value: self.time, fieldNumber: 1)
    }
    if self.detected != false {
      try visitor.visitSingularBoolField(value: self.detected, fieldNumber: 2)
    }
    if self.stable != false {
      try visitor.visitSingularBoolField(value: self.stable, fieldNumber: 3)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Presage_Physiology_DetectionStatus, rhs: Presage_Physiology_DetectionStatus) -> Bool {
    if lhs.time != rhs.time {return false}
    if lhs.detected != rhs.detected {return false}
    if lhs.stable != rhs.stable {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Presage_Physiology_MeasurementWithConfidence: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  public static let protoMessageName: String = _protobuf_package + ".MeasurementWithConfidence"
  public static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "time"),
    2: .same(proto: "value"),
    3: .same(proto: "stable"),
    4: .same(proto: "confidence"),
  ]

  public mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularFloatField(value: &self.time) }()
      case 2: try { try decoder.decodeSingularFloatField(value: &self.value) }()
      case 3: try { try decoder.decodeSingularBoolField(value: &self.stable) }()
      case 4: try { try decoder.decodeSingularFloatField(value: &self.confidence) }()
      default: break
      }
    }
  }

  public func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if self.time != 0 {
      try visitor.visitSingularFloatField(value: self.time, fieldNumber: 1)
    }
    if self.value != 0 {
      try visitor.visitSingularFloatField(value: self.value, fieldNumber: 2)
    }
    if self.stable != false {
      try visitor.visitSingularBoolField(value: self.stable, fieldNumber: 3)
    }
    if self.confidence != 0 {
      try visitor.visitSingularFloatField(value: self.confidence, fieldNumber: 4)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Presage_Physiology_MeasurementWithConfidence, rhs: Presage_Physiology_MeasurementWithConfidence) -> Bool {
    if lhs.time != rhs.time {return false}
    if lhs.value != rhs.value {return false}
    if lhs.stable != rhs.stable {return false}
    if lhs.confidence != rhs.confidence {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Presage_Physiology_Strict: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  public static let protoMessageName: String = _protobuf_package + ".Strict"
  public static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "value"),
  ]

  public mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularFloatField(value: &self.value) }()
      default: break
      }
    }
  }

  public func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if self.value != 0 {
      try visitor.visitSingularFloatField(value: self.value, fieldNumber: 1)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Presage_Physiology_Strict, rhs: Presage_Physiology_Strict) -> Bool {
    if lhs.value != rhs.value {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Presage_Physiology_Pulse: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  public static let protoMessageName: String = _protobuf_package + ".Pulse"
  public static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "rate"),
    2: .same(proto: "trace"),
    3: .standard(proto: "pulse_respiration_quotient"),
    4: .same(proto: "strict"),
  ]

  public mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeRepeatedMessageField(value: &self.rate) }()
      case 2: try { try decoder.decodeRepeatedMessageField(value: &self.trace) }()
      case 3: try { try decoder.decodeRepeatedMessageField(value: &self.pulseRespirationQuotient) }()
      case 4: try { try decoder.decodeSingularMessageField(value: &self._strict) }()
      default: break
      }
    }
  }

  public func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    if !self.rate.isEmpty {
      try visitor.visitRepeatedMessageField(value: self.rate, fieldNumber: 1)
    }
    if !self.trace.isEmpty {
      try visitor.visitRepeatedMessageField(value: self.trace, fieldNumber: 2)
    }
    if !self.pulseRespirationQuotient.isEmpty {
      try visitor.visitRepeatedMessageField(value: self.pulseRespirationQuotient, fieldNumber: 3)
    }
    try { if let v = self._strict {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 4)
    } }()
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Presage_Physiology_Pulse, rhs: Presage_Physiology_Pulse) -> Bool {
    if lhs.rate != rhs.rate {return false}
    if lhs.trace != rhs.trace {return false}
    if lhs.pulseRespirationQuotient != rhs.pulseRespirationQuotient {return false}
    if lhs._strict != rhs._strict {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Presage_Physiology_Trace: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  public static let protoMessageName: String = _protobuf_package + ".Trace"
  public static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "sample"),
  ]

  public mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeRepeatedMessageField(value: &self.sample) }()
      default: break
      }
    }
  }

  public func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if !self.sample.isEmpty {
      try visitor.visitRepeatedMessageField(value: self.sample, fieldNumber: 1)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Presage_Physiology_Trace, rhs: Presage_Physiology_Trace) -> Bool {
    if lhs.sample != rhs.sample {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Presage_Physiology_Breathing: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  public static let protoMessageName: String = _protobuf_package + ".Breathing"
  public static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "rate"),
    2: .standard(proto: "upper_trace"),
    3: .standard(proto: "lower_trace"),
    4: .same(proto: "amplitude"),
    5: .same(proto: "apnea"),
    6: .standard(proto: "respiratory_line_length"),
    7: .same(proto: "baseline"),
    8: .standard(proto: "inhale_exhale_ratio"),
    9: .same(proto: "strict"),
  ]

  public mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeRepeatedMessageField(value: &self.rate) }()
      case 2: try { try decoder.decodeRepeatedMessageField(value: &self.upperTrace) }()
      case 3: try { try decoder.decodeRepeatedMessageField(value: &self.lowerTrace) }()
      case 4: try { try decoder.decodeRepeatedMessageField(value: &self.amplitude) }()
      case 5: try { try decoder.decodeRepeatedMessageField(value: &self.apnea) }()
      case 6: try { try decoder.decodeRepeatedMessageField(value: &self.respiratoryLineLength) }()
      case 7: try { try decoder.decodeRepeatedMessageField(value: &self.baseline) }()
      case 8: try { try decoder.decodeRepeatedMessageField(value: &self.inhaleExhaleRatio) }()
      case 9: try { try decoder.decodeSingularMessageField(value: &self._strict) }()
      default: break
      }
    }
  }

  public func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    if !self.rate.isEmpty {
      try visitor.visitRepeatedMessageField(value: self.rate, fieldNumber: 1)
    }
    if !self.upperTrace.isEmpty {
      try visitor.visitRepeatedMessageField(value: self.upperTrace, fieldNumber: 2)
    }
    if !self.lowerTrace.isEmpty {
      try visitor.visitRepeatedMessageField(value: self.lowerTrace, fieldNumber: 3)
    }
    if !self.amplitude.isEmpty {
      try visitor.visitRepeatedMessageField(value: self.amplitude, fieldNumber: 4)
    }
    if !self.apnea.isEmpty {
      try visitor.visitRepeatedMessageField(value: self.apnea, fieldNumber: 5)
    }
    if !self.respiratoryLineLength.isEmpty {
      try visitor.visitRepeatedMessageField(value: self.respiratoryLineLength, fieldNumber: 6)
    }
    if !self.baseline.isEmpty {
      try visitor.visitRepeatedMessageField(value: self.baseline, fieldNumber: 7)
    }
    if !self.inhaleExhaleRatio.isEmpty {
      try visitor.visitRepeatedMessageField(value: self.inhaleExhaleRatio, fieldNumber: 8)
    }
    try { if let v = self._strict {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 9)
    } }()
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Presage_Physiology_Breathing, rhs: Presage_Physiology_Breathing) -> Bool {
    if lhs.rate != rhs.rate {return false}
    if lhs.upperTrace != rhs.upperTrace {return false}
    if lhs.lowerTrace != rhs.lowerTrace {return false}
    if lhs.amplitude != rhs.amplitude {return false}
    if lhs.apnea != rhs.apnea {return false}
    if lhs.respiratoryLineLength != rhs.respiratoryLineLength {return false}
    if lhs.baseline != rhs.baseline {return false}
    if lhs.inhaleExhaleRatio != rhs.inhaleExhaleRatio {return false}
    if lhs._strict != rhs._strict {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Presage_Physiology_BloodPressure: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  public static let protoMessageName: String = _protobuf_package + ".BloodPressure"
  public static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "phasic"),
  ]

  public mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeRepeatedMessageField(value: &self.phasic) }()
      default: break
      }
    }
  }

  public func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if !self.phasic.isEmpty {
      try visitor.visitRepeatedMessageField(value: self.phasic, fieldNumber: 1)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Presage_Physiology_BloodPressure, rhs: Presage_Physiology_BloodPressure) -> Bool {
    if lhs.phasic != rhs.phasic {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Presage_Physiology_Landmarks: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  public static let protoMessageName: String = _protobuf_package + ".Landmarks"
  public static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "time"),
    2: .same(proto: "value"),
    3: .same(proto: "stable"),
  ]

  public mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularFloatField(value: &self.time) }()
      case 2: try { try decoder.decodeRepeatedMessageField(value: &self.value) }()
      case 3: try { try decoder.decodeSingularBoolField(value: &self.stable) }()
      default: break
      }
    }
  }

  public func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if self.time != 0 {
      try visitor.visitSingularFloatField(value: self.time, fieldNumber: 1)
    }
    if !self.value.isEmpty {
      try visitor.visitRepeatedMessageField(value: self.value, fieldNumber: 2)
    }
    if self.stable != false {
      try visitor.visitSingularBoolField(value: self.stable, fieldNumber: 3)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Presage_Physiology_Landmarks, rhs: Presage_Physiology_Landmarks) -> Bool {
    if lhs.time != rhs.time {return false}
    if lhs.value != rhs.value {return false}
    if lhs.stable != rhs.stable {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Presage_Physiology_Face: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  public static let protoMessageName: String = _protobuf_package + ".Face"
  public static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "blinking"),
    2: .same(proto: "talking"),
    3: .same(proto: "landmarks"),
  ]

  public mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeRepeatedMessageField(value: &self.blinking) }()
      case 2: try { try decoder.decodeRepeatedMessageField(value: &self.talking) }()
      case 3: try { try decoder.decodeRepeatedMessageField(value: &self.landmarks) }()
      default: break
      }
    }
  }

  public func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if !self.blinking.isEmpty {
      try visitor.visitRepeatedMessageField(value: self.blinking, fieldNumber: 1)
    }
    if !self.talking.isEmpty {
      try visitor.visitRepeatedMessageField(value: self.talking, fieldNumber: 2)
    }
    if !self.landmarks.isEmpty {
      try visitor.visitRepeatedMessageField(value: self.landmarks, fieldNumber: 3)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Presage_Physiology_Face, rhs: Presage_Physiology_Face) -> Bool {
    if lhs.blinking != rhs.blinking {return false}
    if lhs.talking != rhs.talking {return false}
    if lhs.landmarks != rhs.landmarks {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Presage_Physiology_Metadata: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  public static let protoMessageName: String = _protobuf_package + ".Metadata"
  public static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "id"),
    2: .standard(proto: "upload_timestamp"),
    3: .standard(proto: "api_version"),
    4: .standard(proto: "sent_at_s"),
    5: .standard(proto: "frame_timestamp"),
    6: .standard(proto: "frame_count"),
  ]

  public mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularStringField(value: &self.id) }()
      case 2: try { try decoder.decodeSingularStringField(value: &self.uploadTimestamp) }()
      case 3: try { try decoder.decodeSingularStringField(value: &self.apiVersion) }()
      case 4: try { try decoder.decodeSingularDoubleField(value: &self.sentAtS) }()
      case 5: try { try decoder.decodeSingularInt64Field(value: &self.frameTimestamp) }()
      case 6: try { try decoder.decodeSingularInt32Field(value: &self.frameCount) }()
      default: break
      }
    }
  }

  public func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if !self.id.isEmpty {
      try visitor.visitSingularStringField(value: self.id, fieldNumber: 1)
    }
    if !self.uploadTimestamp.isEmpty {
      try visitor.visitSingularStringField(value: self.uploadTimestamp, fieldNumber: 2)
    }
    if !self.apiVersion.isEmpty {
      try visitor.visitSingularStringField(value: self.apiVersion, fieldNumber: 3)
    }
    if self.sentAtS != 0 {
      try visitor.visitSingularDoubleField(value: self.sentAtS, fieldNumber: 4)
    }
    if self.frameTimestamp != 0 {
      try visitor.visitSingularInt64Field(value: self.frameTimestamp, fieldNumber: 5)
    }
    if self.frameCount != 0 {
      try visitor.visitSingularInt32Field(value: self.frameCount, fieldNumber: 6)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Presage_Physiology_Metadata, rhs: Presage_Physiology_Metadata) -> Bool {
    if lhs.id != rhs.id {return false}
    if lhs.uploadTimestamp != rhs.uploadTimestamp {return false}
    if lhs.apiVersion != rhs.apiVersion {return false}
    if lhs.sentAtS != rhs.sentAtS {return false}
    if lhs.frameTimestamp != rhs.frameTimestamp {return false}
    if lhs.frameCount != rhs.frameCount {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Presage_Physiology_MetricsBuffer: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  public static let protoMessageName: String = _protobuf_package + ".MetricsBuffer"
  public static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "pulse"),
    2: .same(proto: "breathing"),
    3: .standard(proto: "blood_pressure"),
    4: .same(proto: "face"),
    5: .same(proto: "metadata"),
  ]

  fileprivate class _StorageClass {
    var _pulse: Presage_Physiology_Pulse? = nil
    var _breathing: Presage_Physiology_Breathing? = nil
    var _bloodPressure: Presage_Physiology_BloodPressure? = nil
    var _face: Presage_Physiology_Face? = nil
    var _metadata: Presage_Physiology_Metadata? = nil

    static let defaultInstance = _StorageClass()

    private init() {}

    init(copying source: _StorageClass) {
      _pulse = source._pulse
      _breathing = source._breathing
      _bloodPressure = source._bloodPressure
      _face = source._face
      _metadata = source._metadata
    }
  }

  fileprivate mutating func _uniqueStorage() -> _StorageClass {
    if !isKnownUniquelyReferenced(&_storage) {
      _storage = _StorageClass(copying: _storage)
    }
    return _storage
  }

  public mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    _ = _uniqueStorage()
    try withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      while let fieldNumber = try decoder.nextFieldNumber() {
        // The use of inline closures is to circumvent an issue where the compiler
        // allocates stack space for every case branch when no optimizations are
        // enabled. https://github.com/apple/swift-protobuf/issues/1034
        switch fieldNumber {
        case 1: try { try decoder.decodeSingularMessageField(value: &_storage._pulse) }()
        case 2: try { try decoder.decodeSingularMessageField(value: &_storage._breathing) }()
        case 3: try { try decoder.decodeSingularMessageField(value: &_storage._bloodPressure) }()
        case 4: try { try decoder.decodeSingularMessageField(value: &_storage._face) }()
        case 5: try { try decoder.decodeSingularMessageField(value: &_storage._metadata) }()
        default: break
        }
      }
    }
  }

  public func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    try withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every if/case branch local when no optimizations
      // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
      // https://github.com/apple/swift-protobuf/issues/1182
      try { if let v = _storage._pulse {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 1)
      } }()
      try { if let v = _storage._breathing {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 2)
      } }()
      try { if let v = _storage._bloodPressure {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 3)
      } }()
      try { if let v = _storage._face {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 4)
      } }()
      try { if let v = _storage._metadata {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 5)
      } }()
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Presage_Physiology_MetricsBuffer, rhs: Presage_Physiology_MetricsBuffer) -> Bool {
    if lhs._storage !== rhs._storage {
      let storagesAreEqual: Bool = withExtendedLifetime((lhs._storage, rhs._storage)) { (_args: (_StorageClass, _StorageClass)) in
        let _storage = _args.0
        let rhs_storage = _args.1
        if _storage._pulse != rhs_storage._pulse {return false}
        if _storage._breathing != rhs_storage._breathing {return false}
        if _storage._bloodPressure != rhs_storage._bloodPressure {return false}
        if _storage._face != rhs_storage._face {return false}
        if _storage._metadata != rhs_storage._metadata {return false}
        return true
      }
      if !storagesAreEqual {return false}
    }
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Presage_Physiology_Metrics: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  public static let protoMessageName: String = _protobuf_package + ".Metrics"
  public static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "breathing"),
  ]

  public mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularMessageField(value: &self._breathing) }()
      default: break
      }
    }
  }

  public func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    try { if let v = self._breathing {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 1)
    } }()
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Presage_Physiology_Metrics, rhs: Presage_Physiology_Metrics) -> Bool {
    if lhs._breathing != rhs._breathing {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}
