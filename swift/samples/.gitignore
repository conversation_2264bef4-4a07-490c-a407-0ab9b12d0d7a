# macOS generated files
**/.DS_Store
.AppleDouble
.LSOverride

# Xcode specific
# Compiled object files and app products
*.o
*.dylib
*.so
*.app
*.ipa
*.dSYM.zip
*.dSYM
*.lproj/
*.swiftdoc
*.swiftmodule

# Xcode project files
xcuserdata/
*.xcworkspace
*.xcuserstate

# Derived data and build output
build/
DerivedData/
**/.build/
# fastlane builds
.bundle

# Swift Package Manager
#
# Add this line if you want to avoid checking in source code from Swift Package Manager dependencies.
# Packages/
# Package.pins
# Package.resolved
# *.xcodeproj
#
# Xcode automatically generates this directory with a .xcworkspacedata file and xcuserdata
# hence it is not needed unless you have added a package configuration file to your project
.swiftpm

# CocoaPods
# Pods/
# Add this line if you want to avoid checking in source code from the Xcode workspace
*.xcworkspace

# Carthage
Carthage/Build/

# fastlane
fastlane/report.xml
fastlane/Preview.html
fastlane/screenshots/**/*.png
fastlane/test_output

# Obj-C/Swift specific
*.hmap

## App packaging
*.ipa
*.dSYM.zip
*.dSYM

## Playgrounds
timeline.xctimeline
playground.xcworkspace


# Presage specific files
PresageService-Info.plist
amplify_outputs.json
