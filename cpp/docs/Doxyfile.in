# Doxyfile 1.9.1

PROJECT_NAME           = "SmartSpectra C++ SDK"
PROJECT_BRIEF          = Measure human vitals from video with SmartSpectra C++ SDK.
PROJECT_LOGO           = docs/images/presage_logo_tri_mini_square55x55.png


# Note: doxygen will be run from within smartspectra/cpp of the physiology monorepo directory
#       (cpp/ in the smartspectra repo)
OUTPUT_DIRECTORY       = docs/generated
SHOW_FILES             = NO

# input / exclude
INPUT                  = .
RECURSIVE              = YES
EXCLUDE                = .idea \
                         .clwb \
                         .venv

EXCLUDE_SYMLINKS       = YES
EXCLUDE_PATTERNS       = cmake-build-* \
                         build* \
                         requirements*.txt \
                         .gitlab-ci.yml

# page generation & styling
USE_MDFILE_AS_MAINPAGE = ./README.md
HTML_HEADER            = docs/header.html

HTML_EXTRA_STYLESHEET  = @AWESOME_CSS_DIR@/doxygen-awesome.css \
                         @AWESOME_CSS_DIR@/doxygen-awesome-sidebar-only.css \
                         @AWESOME_CSS_DIR@/doxygen-awesome-sidebar-only-darkmode-toggle.css

HTML_EXTRA_FILES       = @AWESOME_CSS_DIR@/doxygen-awesome-darkmode-toggle.js \
                         @AWESOME_CSS_DIR@/doxygen-awesome-fragment-copy-button.js \
                         @AWESOME_CSS_DIR@/doxygen-awesome-paragraph-link.js \
                         @AWESOME_CSS_DIR@/doxygen-awesome-interactive-toc.js \
                         @AWESOME_CSS_DIR@/doxygen-awesome-tabs.js

GENERATE_TREEVIEW      = YES
GENERATE_LATEX         = NO

INCLUDE_FILE_PATTERNS  = *.h *.hpp *.h.in *.hpp.in

DOT_IMAGE_FORMAT       = svg
DOT_TRANSPARENT        = YES
