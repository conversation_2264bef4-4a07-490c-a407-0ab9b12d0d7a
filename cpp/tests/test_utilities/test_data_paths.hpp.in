////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// Created by <PERSON> on 9/3/24.
//    Based on https://github.com/Algomorph/NeuralTracking/blob/main/cpp/tests/test_utils/test_data_paths.hpp.in,
//    Which was created by <PERSON> (https://github.com/Algomorph) on 3/01/21, copyright (c) 2020 <PERSON>
// Copyright (C) 2024 Presage Security, Inc.
//
// This program is free software; you can redistribute it and/or
// modify it under the terms of the GNU Lesser General Public
// License as published by the Free Software Foundation; either
// version 3 of the License, or (at your option) any later version.
//
// This program is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
// Lesser General Public License for more details.
//
// You should have received a copy of the GNU Lesser General Public License
// along with this program; if not, write to the Free Software Foundation,
// Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

#pragma once

#cmakedefine GENERATED_TEST_DATA_DIRECTORY "@GENERATED_TEST_DATA_DIRECTORY@"
#cmakedefine STATIC_TEST_DATA_DIRECTORY "@STATIC_TEST_DATA_DIRECTORY@"
