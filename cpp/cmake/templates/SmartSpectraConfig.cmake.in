###########################################################
# SmartSpectraConfig.cmake.in
# Created by <PERSON> on 8/19/2024.
# Copyright (C) 2024 Presage Security, Inc.
#
# This program is free software; you can redistribute it and/or
# modify it under the terms of the GNU Lesser General Public
# License as published by the Free Software Foundation; either
# version 3 of the License, or (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
# Lesser General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with this program; if not, write to the Free Software Foundation,
# Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.
###########################################################

find_package(PhysiologyEdge REQUIRED)

list(APPEND CMAKE_MODULE_PATH "${CMAKE_CURRENT_LIST_DIR}/modules")

if (UNIX AND NOT APPLE)
    set(LINUX TRUE)
endif ()

if (LINUX)
    find_package(V4L REQUIRED)
endif ()

if (NOT APPLE)
    find_package(OpenGL REQUIRED OpenGL GLES3)
endif ()

set(PROVIDES_ON_PREM @BUILD_ON_PREM@)
if (PROVIDES_ON_PREM)
    include(FetchContent)

    set(REPROC++ ON)
    set(REPROC_INSTALL ON)

    FetchContent_Declare(
            reproc
            GIT_REPOSITORY https://github.com/DaanDeMeyer/reproc.git
            GIT_TAG        v14.2.5
    )

    FetchContent_MakeAvailable(reproc)
endif ()

include(${CMAKE_CURRENT_LIST_DIR}/@<EMAIL>)
