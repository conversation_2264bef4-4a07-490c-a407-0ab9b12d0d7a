###########################################################
# cmake_uninstall.cmake.in
# Created by <PERSON> on 8/19/2024.
# Copyright (C) 2024 Presage Security, Inc.
#
# This program is free software; you can redistribute it and/or
# modify it under the terms of the GNU Lesser General Public
# License as published by the Free Software Foundation; either
# version 3 of the License, or (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
# Lesser General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with this program; if not, write to the Free Software Foundation,
# Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.
###########################################################
#
# File that provides "make uninstall" (or "ninja uninstall") target
# We use the file 'install_manifest.txt' to get a list of installed files
#
############################################################

if(NOT EXISTS "@CMAKE_BINARY_DIR@/install_manifest.txt")
  message(FATAL_ERROR "Cannot find install manifest: \"@CMAKE_BINARY_DIR@/install_manifest.txt\"")
endif()

file(READ "@CMAKE_BINARY_DIR@/install_manifest.txt" files)
string(REGEX REPLACE "\n" ";" files "${files}")

# List to keep track of non-empty directories
set(non_empty_directories "")

# Function to remove empty directories
# Function to remove empty directories
function(remove_empty_directories path)
  # Copy the parent scope variable to a local variable
  set(local_non_empty_directories "${non_empty_directories}")

  get_filename_component(parent_path "${path}" DIRECTORY)
  while(IS_DIRECTORY "${path}" AND
          NOT "${parent_path}" STREQUAL "@CMAKE_INSTALL_PREFIX@" AND
          NOT "${path}" STREQUAL "@CMAKE_INSTALL_FULL_INCLUDEDIR@" AND
          NOT "${path}" STREQUAL "@CMAKE_INSTALL_FULL_BINDIR@" AND
          NOT "${path}" STREQUAL "@CMAKE_INSTALL_FULL_LIBDIR@" AND
          NOT "${path}" STREQUAL "@CMAKE_INSTALL_FULL_LIBDIR@/cmake")

    file(GLOB children RELATIVE "${path}" "${path}/*")
    if(children)
      # Directory is not empty, add to the list if it's not already there
      list(FIND local_non_empty_directories "${path}" index)
      if(index EQUAL -1)
        list(APPEND local_non_empty_directories "${path}")
      endif()
      break()
    endif()

    message(STATUS "Removing empty directory: ${path}")
    file(REMOVE_RECURSE "${path}")

    # Remove from local_non_empty_directories if it was there
    list(REMOVE_ITEM local_non_empty_directories "${path}")

    # Move to parent directory
    get_filename_component(path "${path}" DIRECTORY)
    get_filename_component(parent_path "${path}" DIRECTORY)
  endwhile()

  # Set the parent scope variable from the local variable
  set(non_empty_directories "${local_non_empty_directories}" PARENT_SCOPE)
endfunction()

foreach(file ${files})
  message(STATUS "Uninstalling $ENV{DESTDIR}${file}")
  if(IS_SYMLINK "$ENV{DESTDIR}${file}" OR EXISTS "$ENV{DESTDIR}${file}")
    execute_process(
            COMMAND "@CMAKE_COMMAND@" -E remove "$ENV{DESTDIR}${file}"
            OUTPUT_VARIABLE rm_out
            RESULT_VARIABLE rm_retval
    )
    if(NOT "${rm_retval}" STREQUAL 0)
      message(FATAL_ERROR "Problem when removing $ENV{DESTDIR}${file}")
    endif()

    # Call function to remove empty directories
    get_filename_component(file_directory "$ENV{DESTDIR}${file}" DIRECTORY)
    remove_empty_directories("${file_directory}")
  else()
    message(STATUS "File $ENV{DESTDIR}${file} does not exist.")
  endif()
endforeach()


if(NOT "${non_empty_directories}" STREQUAL "")
  message(STATUS "Warning: the following directories were not removed because they are not empty:")
  foreach(dir ${non_empty_directories})
    message(STATUS "  - ${dir}")
  endforeach()
endif()
