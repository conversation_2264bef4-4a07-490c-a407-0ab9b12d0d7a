[build-system]
requires = ["setuptools>=69", "wheel"]
build-backend = "setuptools.build_meta"

[project]
dynamic = ["version"]
name = "SmartSpectra-Protobuf"
authors = [{name = "Presage Security, Inc.", email = "<EMAIL>"}]
description = "SmartSpectra Protobuf python package defines the SmartSpectra OnPrem gRPC Service."
requires-python = ">=3.11"
dependencies = ["grpcio==1.63.0","grpcio-tools==1.63.0"]

[tool.setuptools]
packages = ["smartspectra", "smartspectra.on_prem"]

[tool.setuptools.dynamic]
version = {attr = "smartspectra.__version__"}



