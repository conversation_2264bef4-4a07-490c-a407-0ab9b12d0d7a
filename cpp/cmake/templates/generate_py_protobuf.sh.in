#!/bin/bash
set -e

@Python3_EXECUTABLE@ -m grpc_tools.protoc --proto_path="@PROJECT_SOURCE_DIR@" --proto_path="@MAIN_EDGE_INCLUDE@" \
  --python_out="@arg_PACKAGE_DIRECTORY@" --pyi_out="@arg_PACKAGE_DIRECTORY@" \
  @GRPC_PLUGIN_OPTIONS@ "@PROTOBUF_SOURCE_FILE_RELATIVE@"

# Convert the colon-separated list back to an array
IFS=':' read -r -a PREPEND_PATHS <<< "@arg_PREPEND_PATHS@"

PY_FILE=@OUTPUT_PY_FILE@
for dir in "${PREPEND_PATHS[@]}"; do
    sed -i.bak -E "s|from ${dir} import (.*)|from @PHYSIOLOGY_PYTHON_MODULE_NAME@.${dir} import \1|" "$PY_FILE"
done
GRPC_FILE=@OUTPUT_GRPC_PY_FILE@
if [ -f "$GRPC_FILE" ]; then
    for dir in "${PREPEND_PATHS[@]}"; do
        sed -i.bak -E "s|from ${dir} import (.*)|from @PHYSIOLOGY_PYTHON_MODULE_NAME@.${dir} import \1|" "$GRPC_FILE"
    done
fi

GRPC_FILE=@OUTPUT_GRPC_PY_FILE@
if [ -f "$GRPC_FILE" ]; then
    sed -i.bak -E 's|from @PROTOBUF_SOURCE_DIRECTORY_RELATIVE@ import (.*)|from smartspectra.@PROTOBUF_SOURCE_DIRECTORY_RELATIVE@ import \1|' "$GRPC_FILE"
fi
