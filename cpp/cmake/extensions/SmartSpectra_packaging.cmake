# honor CMAKE_INSTALL_PREFIX instead of CPACK_PACKAGING_INSTALL_PREFIX
set(CPACK_SET_DESTDIR ON)

set(CPACK_PACKAGE_NAME "lib${PROJECT_NAME_LOWER}-dev")
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY "SmartSpectra C++ SDK")
set(CPACK_PACKAGE_DESCRIPTION ${SMART_SPECTRA_PACKAGE_DESCRIPTION})
set(CPACK_PACKAGE_VENDOR "Presage Security, Inc.")
set(CPACK_RESOURCE_FILE_LICENSE "${CMAKE_CURRENT_SOURCE_DIR}/LICENSE")
set(CPACK_PACKAGE_CONTACT "Presage Technologies Maintainers <<EMAIL>>")
set(CPACK_PACKAGE_VERSION_MAJOR "${SMART_SPECTRA_VERSION_MAJOR}")
set(CPACK_PACKAGE_VERSION_MINOR "${SMART_SPECTRA_VERSION_MINOR}")
set(CPACK_PACKAGE_VERSION_PATCH "${SMART_SPECTRA_VERSION_PATCH}")
set(CPACK_PACKAGE_VERSION "${SMART_SPECTRA_VERSION_PLAIN}${SMART_SPECTRA_EDGE_VERSION_PRERELEASE_DEBIAN_POSTFIX}")

if (${TARGET_ARCHITECTURE} STREQUAL ${LINUX_AMD64_TARGET_ARCHITECTURE})
    set(CPACK_DEBIAN_PACKAGE_ARCHITECTURE "amd64")
    set(CPACK_RPM_PACKAGE_ARCHITECTURE "x86_64")
elseif (${TARGET_ARCHITECTURE} STREQUAL ${LINUX_ARM64_TARGET_ARCHITECTURE})
    set(CPACK_DEBIAN_PACKAGE_ARCHITECTURE "arm64")
    set(CPACK_RPM_PACKAGE_ARCHITECTURE "aarch64")
endif ()

set(CPACK_PROJECT_CONFIG_FILE ${CMAKE_SOURCE_DIR}/cmake/extensions/SmartSpectra_package_config.cmake)

# rpm options
set(CPACK_RPM_COMPONENT_INSTALL FALSE)
set(CPACK_RPM_PACKAGE_URL "https://www.presagetechnologies.com")
set(CPACK_RPM_PACKAGE_LICENSE "LGPL v3.0")

# deb options
set(CPACK_DEB_COMPONENT_INSTALL FALSE)
set(CPACK_DEBIAN_PACKAGE_PRIORITY "optional")
set(CPACK_DEBIAN_PACKAGE_SECTION "libdevel")
set(CPACK_DEBIAN_PACKAGE_HOMEPAGE "https://www.presagetechnologies.com")

# dependencies
set(CPACK_DEBIAN_PACKAGE_SHLIBDEPS TRUE)

# we require Physiology Edge of the same version as SmartSpectra or greater:
string(CONCAT PHYSIOLOGY_EDGE_DEB_PACKAGE "libphysiologyedge-dev (>= " ${CPACK_PACKAGE_VERSION} ")")

set(CPACK_DEBIAN_PACKAGE_DEPENDS ${PHYSIOLOGY_EDGE_DEB_PACKAGE})

set(CPACK_DEBIAN_PACKAGE_CONFLICTS "${CPACK_PACKAGE_NAME}")
set(CPACK_DEBIAN_PACKAGE_REPLACES "${CPACK_PACKAGE_NAME}")
set(CPACK_DEBIAN_PACKAGE_PROVIDES "${CPACK_PACKAGE_NAME}")

include(CPack)
