//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// benchmarking.h
// Created by <PERSON> on 2/16/24.
// Copyright (C) 2024 Presage Security, Inc.
//
// This program is free software; you can redistribute it and/or
// modify it under the terms of the GNU Lesser General Public
// License as published by the Free Software Foundation; either
// version 3 of the License, or (at your option) any later version.
//
// This program is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
// Lesser General Public License for more details.
//
// You should have received a copy of the GNU Lesser General Public License
// along with this program; if not, write to the Free Software Foundation,
// Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.
//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

#pragma once
// === standard library includes (if any) ===
// === third-party includes (if any) ===
#include <mediapipe/framework/port/opencv_video_inc.h>
#include <absl/status/status.h>
// === local includes (if any) ===
#include <smartspectra/video_source/camera/camera.hpp>

namespace presage::smartspectra::container::benchmarking{

absl::Status HandleCameraBenchmarking(
    int64_t& i_frame,
    std::chrono::duration<double>& interval_capture_time,
    std::chrono::duration<double>& interval_frame_time,
    std::chrono::time_point<std::chrono::high_resolution_clock> frame_loop_start,
    std::chrono::time_point<std::chrono::high_resolution_clock> frame_capture_end,
    int64_t frame_interval,
    int interframe_delay_ms,
    int verbosity_level
);

} // namespace presage::smartspectra::container::benchmarking
