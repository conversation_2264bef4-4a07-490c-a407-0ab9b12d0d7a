set(HOST_SYSTEM_PROCESSOR_ARCHITECTURE ${CMAKE_HOST_SYSTEM_PROCESSOR})
if (${CMAKE_HOST_SYSTEM_PROCESSOR} STREQUAL "x86_64")
    # to stick to used convention
    set(HOST_SYSTEM_PROCESSOR_ARCHITECTURE "amd64")
endif ()

string(TOLOWER "${CMAKE_HOST_SYSTEM_NAME}" CMAKE_HOST_SYSTEM_NAME_LC)
set(CMAKE_HOST_ARCHITECTURE "${CMAKE_HOST_SYSTEM_NAME_LC}_${HOST_SYSTEM_PROCESSOR_ARCHITECTURE}")
message(STATUS "Build host architecture: ${CMAKE_HOST_ARCHITECTURE}")

# used in
set(LINUX_ARM64_TARGET_ARCHITECTURE "linux_arm64")
set(LINUX_AMD64_TARGET_ARCHITECTURE "linux_amd64")

list(APPEND TARGET_ARCHITECTURES ${LINUX_ARM64_TARGET_ARCHITECTURE})
list(APPEND TARGET_ARCHITECTURES ${LINUX_AMD64_TARGET_ARCHITECTURE})
list(FIND TARGET_ARCHITECTURES ${CMAKE_HOST_ARCHITECTURE} CMAKE_HOST_ARCHITECTURE_INDEX)
if (NOT CMAKE_HOST_ARCHITECTURE_INDEX EQUAL -1)
    list(APPEND TARGET_ARCHITECTURES ${CMAKE_HOST_ARCHITECTURE_INDEX})
endif ()

if (CMAKE_TOOLCHAIN_FILE)
    message(STATUS "CMAKE_TOOLCHAIN_FILE: ${CMAKE_TOOLCHAIN_FILE}")
endif ()
if (NOT TARGET_ARCHITECTURE)
    set(TARGET_ARCHITECTURE ${CMAKE_HOST_ARCHITECTURE})
endif ()
message(STATUS "TARGET_ARCHITECTURE: ${TARGET_ARCHITECTURE}")
